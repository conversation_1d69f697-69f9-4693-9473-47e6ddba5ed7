<?php

if (!defined('BASEPATH')) {
    exit('No direct script access allowed');
}

class Studentsubjectattendence_model extends CI_Model
{

    public function __construct()
    {
        parent::__construct();
        $this->current_session = $this->setting_model->getCurrentSession();
        $this->current_date    = $this->setting_model->getDateYmd();
    }

    public function add($insert_array, $update_array)
    {

        $this->db->trans_start();
        $this->db->trans_strict(false);
        if (!empty($insert_array)) {

            $this->db->insert_batch('student_subject_attendances', $insert_array);
        }
        if (!empty($update_array)) {
            $this->db->update_batch('student_subject_attendances', $update_array, 'id');
        }
        $this->db->trans_complete();

        if ($this->db->trans_status() === false) {

            $this->db->trans_rollback();
            return false;
        } else {

            $this->db->trans_commit();
            return true;
        }
    }
    
    public function add_alternate($insert_array, $update_array)
    {
        $this->db->trans_start();
        $this->db->trans_strict(false);
        if (!empty($insert_array)) {
    
            $this->db->insert_batch('alternate_student_subject_attendances', $insert_array);
        }
        if (!empty($update_array)) {
            $this->db->update_batch('alternate_student_subject_attendances', $update_array, 'id');
        }
        $this->db->trans_complete();
    
        if ($this->db->trans_status() === false) {
    
            $this->db->trans_rollback();
            return false;
        } else {
    
            $this->db->trans_commit();
            return true;
        }
    }
    
    public function add_extra($insert_array, $update_array)
    {
        $this->db->trans_start();
        $this->db->trans_strict(false);
        if (!empty($insert_array)) {
    
            $this->db->insert_batch('extra_student_attendances', $insert_array);
        }
        if (!empty($update_array)) {
            $this->db->update_batch('extra_student_attendances', $update_array, 'id');
        }
        $this->db->trans_complete();
    
        if ($this->db->trans_status() === false) {
    
            $this->db->trans_rollback();
            return false;
        } else {
    
            $this->db->trans_commit();
            return true;
        }
    }
    
    public function add_event_attendance($insert_array, $update_array)
    {
        $this->db->trans_start();
        $this->db->trans_strict(false);
        if (!empty($insert_array)) {
    
            $this->db->insert_batch('event_day_attendances', $insert_array);
        }
        if (!empty($update_array)) {
            $this->db->update_batch('event_day_attendances', $update_array, 'id');
        }
        $this->db->trans_complete();
    
        if ($this->db->trans_status() === false) {
    
            $this->db->trans_rollback();
            return false;
        } else {
    
            $this->db->trans_commit();
            return true;
        }
    }

    public function searchAttendenceClassSection($class_id, $section_id, $subject_timetable_id, $date)
    {
        $sql1  = "SELECT  IFNULL(student_subject_attendances.id, '0') as student_subject_attendance_id,student_subject_attendances.subject_timetable_id,student_subject_attendances.attendence_type_id, IFNULL(student_subject_attendances.date, 'xxx') as date,student_subject_attendances.remark,students.*,student_session.id as student_session_id FROM students INNER JOIN student_session on students.id=student_session.student_id and student_session.class_id=" . $this->db->escape($class_id) . " and student_session.section_id =" . $this->db->escape($section_id) . "  AND student_session.session_id=" . $this->db->escape($this->current_session) . " LEFT JOIN subject_timetable ON subject_timetable.subject_group_subject_id = student_session.subject_id AND subject_timetable.id=" . $this->db->escape($subject_timetable_id) . " AND student_session.is_compulsory = 0 AND student_session.subject_id > 0 LEFT JOIN student_subject_attendances on student_session.id=student_subject_attendances.student_session_id and student_subject_attendances.subject_timetable_id=" . $this->db->escape($subject_timetable_id) . " and date=" . $this->db->escape($date) . " 
        where `students`.`is_active`='yes' ";

        //check is subject optional
        $sqlCheck = "SELECT sgs.is_optional,sgs.subject_id From subject_group_subjects as sgs join subject_timetable as st on st.subject_group_subject_id = sgs.id where 1 and st.id = ".$this->db->escape($subject_timetable_id);
        $query1 = $this->db->query($sqlCheck);
        $check = $query1->row();

        //echo "<pre>";
        //print_r($check);
       //die;

        if($check->is_optional == 0){
            $sql2 = "SELECT  IFNULL(student_subject_attendances.id, '0') as student_subject_attendance_id,student_subject_attendances.subject_timetable_id,student_subject_attendances.attendence_type_id, IFNULL(student_subject_attendances.date, 'xxx') as date,student_subject_attendances.remark,students.*,student_session.id as student_session_id,student_session.subject_id as session_subject,student_session.is_compulsory FROM students 
        INNER JOIN student_session on students.id=student_session.student_id and student_session.class_id=" . $this->db->escape($class_id) . " and student_session.section_id =" . $this->db->escape($section_id) . "  AND student_session.session_id=" . $this->db->escape($this->current_session) . "
        LEFT JOIN student_subject_attendances on student_session.id=student_subject_attendances.student_session_id and student_subject_attendances.subject_timetable_id=" . $this->db->escape($subject_timetable_id) . " and date=" . $this->db->escape($date) . " 
        where `students`.`is_active`='yes'";
        }else{
            $sql2 = "SELECT  IFNULL(student_subject_attendances.id, '0') as student_subject_attendance_id,student_subject_attendances.subject_timetable_id,student_subject_attendances.attendence_type_id, IFNULL(student_subject_attendances.date, 'xxx') as date,student_subject_attendances.remark,students.*,student_session.id as student_session_id FROM students 
        INNER JOIN student_session_multiclass on students.id=student_session_multiclass.student_id and student_session_multiclass.class_id=" . $this->db->escape($class_id) . " and student_session_multiclass.section_id =" . $this->db->escape($section_id) . "  AND student_session_multiclass.session_id=" . $this->db->escape($this->current_session) . " AND student_session_multiclass.subject_id=" . $this->db->escape($check->subject_id) . "
        INNER JOIN student_session on students.id=student_session.student_id AND student_session.session_id=" . $this->db->escape($this->current_session) . "
        LEFT JOIN student_subject_attendances on student_session.id=student_subject_attendances.student_session_id and student_subject_attendances.subject_timetable_id=" . $this->db->escape($subject_timetable_id) . " and date=" . $this->db->escape($date) . " 
        where `students`.`is_active`='yes'";
        }
        

        $query = $this->db->query($sql2);
        //echo $this->db->last_query();
        //die;
        return $query->result_array();
    }
    
    public function searchAlternateAttendenceClassSection($class_id, $section_id, $subject_timetable_id, $date)
    {
        $sql1  = "SELECT  IFNULL(alternate_student_subject_attendances.id, '0') as student_subject_attendance_id,alternate_student_subject_attendances.subject_timetable_id,alternate_student_subject_attendances.attendence_type_id, IFNULL(alternate_student_subject_attendances.date, 'xxx') as date,alternate_student_subject_attendances.remark,students.*,student_session.id as student_session_id FROM students INNER JOIN student_session on students.id=student_session.student_id and student_session.class_id=" . $this->db->escape($class_id) . " and student_session.section_id =" . $this->db->escape($section_id) . "  AND student_session.session_id=" . $this->db->escape($this->current_session) . " LEFT JOIN subject_timetable ON subject_timetable.subject_group_subject_id = student_session.subject_id AND subject_timetable.id=" . $this->db->escape($subject_timetable_id) . " AND student_session.is_compulsory = 0 AND student_session.subject_id > 0 LEFT JOIN alternate_student_subject_attendances on student_session.id=alternate_student_subject_attendances.student_session_id and alternate_student_subject_attendances.subject_timetable_id=" . $this->db->escape($subject_timetable_id) . " and date=" . $this->db->escape($date) . " 
        where `students`.`is_active`='yes' ";

        $sqlCheck = "SELECT sgs.is_optional,sgs.subject_id From subject_group_subjects as sgs join subject_timetable as st on st.subject_group_subject_id = sgs.id where 1 and st.id = ".$this->db->escape($subject_timetable_id);
        $query1 = $this->db->query($sqlCheck);
        $check = $query1->row();

        if($check->is_optional == 0){
            $sql2 = "SELECT  IFNULL(alternate_student_subject_attendances.id, '0') as student_subject_attendance_id,alternate_student_subject_attendances.subject_timetable_id,alternate_student_subject_attendances.attendence_type_id,alternate_student_subject_attendances.teacher_id,CONCAT_WS(' ',staff.name,staff.surname) as teacher_name, IFNULL(alternate_student_subject_attendances.date, 'xxx') as date,alternate_student_subject_attendances.remark,students.*,student_session.id as student_session_id,student_session.subject_id as session_subject,student_session.is_compulsory FROM students 
        INNER JOIN student_session on students.id=student_session.student_id and student_session.class_id=" . $this->db->escape($class_id) . " and student_session.section_id =" . $this->db->escape($section_id) . "  AND student_session.session_id=" . $this->db->escape($this->current_session) . "
        LEFT JOIN alternate_student_subject_attendances on student_session.id=alternate_student_subject_attendances.student_session_id and alternate_student_subject_attendances.subject_timetable_id=" . $this->db->escape($subject_timetable_id) . " and date=" . $this->db->escape($date) . " 
        LEFT JOIN staff on staff.id = alternate_student_subject_attendances.teacher_id
        where `students`.`is_active`='yes'";
        }else{
            $sql2 = "SELECT  IFNULL(alternate_student_subject_attendances.id, '0') as student_subject_attendance_id,alternate_student_subject_attendances.subject_timetable_id,alternate_student_subject_attendances.attendence_type_id,alternate_student_subject_attendances.teacher_id,CONCAT_WS(' ',staff.name,staff.surname) as teacher_name, IFNULL(alternate_student_subject_attendances.date, 'xxx') as date,alternate_student_subject_attendances.remark,students.*,student_session.id as student_session_id FROM students 
        INNER JOIN student_session_multiclass on students.id=student_session_multiclass.student_id and student_session_multiclass.class_id=" . $this->db->escape($class_id) . " and student_session_multiclass.section_id =" . $this->db->escape($section_id) . "  AND student_session_multiclass.session_id=" . $this->db->escape($this->current_session) . " AND student_session_multiclass.subject_id=" . $this->db->escape($check->subject_id) . "
        INNER JOIN student_session on students.id=student_session.student_id AND student_session.session_id=" . $this->db->escape($this->current_session) . "
        LEFT JOIN alternate_student_subject_attendances on student_session.id=alternate_student_subject_attendances.student_session_id and alternate_student_subject_attendances.subject_timetable_id=" . $this->db->escape($subject_timetable_id) . " and date=" . $this->db->escape($date) . " 
        LEFT JOIN staff on staff.id = alternate_student_subject_attendances.teacher_id
        where `students`.`is_active`='yes'";
        }
        

        $query = $this->db->query($sql2);
        //echo $this->db->last_query();
        //die;
        return $query->result_array();
    }

    public function searchExtraAttendenceClassSection($class_id, $section_id, $date,$subject_timetable_id)
    {

        $sql2 = "SELECT  IFNULL(extra_student_attendances.id, '0') as student_subject_attendance_id,extra_student_attendances.attendence_type_id,extra_student_attendances.teacher_id,CONCAT_WS(' ',staff.name,staff.surname) as teacher_name, IFNULL(extra_student_attendances.date, 'xxx') as date,extra_student_attendances.remark,students.*,student_session.id as student_session_id FROM students 
        INNER JOIN student_session on students.id=student_session.student_id and student_session.class_id=" . $this->db->escape($class_id) . " and student_session.section_id =" . $this->db->escape($section_id) . "  AND student_session.session_id=" . $this->db->escape($this->current_session) . "
        LEFT JOIN extra_student_attendances on student_session.id=extra_student_attendances.student_session_id and extra_student_attendances.date=" . $this->db->escape($date) . " and extra_student_attendances.subject_id=" . $this->db->escape($subject_timetable_id) . " 
        LEFT JOIN staff on staff.id = extra_student_attendances.teacher_id
        where `students`.`is_active`='yes'";

        $query = $this->db->query($sql2);
        //echo $this->db->last_query();
        //die;
        return $query->result_array();
    }
    
    public function searchEventDayAttendenceClassSection($class_id, $section_id, $date)
    {

        $sql2 = "SELECT  IFNULL(event_day_attendances.id, '0') as student_subject_attendance_id,event_day_attendances.attendance_type,event_day_attendances.teacher_id,CONCAT_WS(' ',staff.name,staff.surname) as teacher_name, IFNULL(event_day_attendances.date, 'xxx') as date,event_day_attendances.remark,students.*,student_session.id as student_session_id FROM students 
        INNER JOIN student_session on students.id=student_session.student_id and student_session.class_id=" . $this->db->escape($class_id) . " and student_session.section_id =" . $this->db->escape($section_id) . "  AND student_session.session_id=" . $this->db->escape($this->current_session) . "
        LEFT JOIN event_day_attendances on student_session.id=event_day_attendances.student_session_id and event_day_attendances.date=" . $this->db->escape($date) . " 
        LEFT JOIN staff on staff.id = event_day_attendances.teacher_id
        where `students`.`is_active`='yes'";
        
        $query = $this->db->query($sql2);
        return $query->result_array();
    }
    
    public function getAlternateStudentsMontlyAttendence($class_id, $section_id, $total_days, $subject_id)
    {
        $student_array = array();
        $student_array['class_students'] = $this->student_model->searchByClassSectionWithSession($class_id, $section_id);
    
        $student_array['students_attendances'] = array();
    
        for ($i = 0; $i < count($total_days); $i++) {
            $date = date('Y-m-d', strtotime($total_days[$i]));
            $day = date('l', strtotime($date));
            $students_time_table = $this->searchByAlternateStudentsAttendanceByDate($class_id, $section_id, $day, $date, $subject_id);
            $a = array();
            $a['date'] = $date;
            $a['day'] = $day;
            $a['subjects'] = array();
            $a['students'] = array();
    
            if (!empty($students_time_table)) {
                $students_time_table = json_decode($students_time_table);
    
                $a['subjects'] = ($students_time_table->subjects);
                foreach ($students_time_table->student_record as $students_time_table_key => $students_time_table_value) {
                    $a['students'][$students_time_table_value->id] = ($students_time_table->student_record[$students_time_table_key]);
                }
            }
            $student_array['students_attendances'][$date] = $a;
        }
    
        return $student_array;
    }
    
    public function searchByAlternateStudentsAttendanceByDate($class_id, $section_id, $day, $date, $subject_id)
    {
        $sql = "SELECT subject_timetable.*,subjects.id as `subject_id`,subjects.name,subjects.code,subjects.type FROM `subject_timetable` INNER JOIN subject_group_subjects on subject_group_subjects.id=subject_timetable.subject_group_subject_id INNER JOIN subjects on subjects.id=subject_group_subjects.subject_id WHERE subject_timetable.class_id=" . $this->db->escape($class_id) . " AND subject_timetable.section_id=" . $this->db->escape($section_id) . " and subject_timetable.session_id=" . $this->db->escape($this->current_session) . " and subject_timetable.day=" . $this->db->escape($day);
        if($subject_id !=""){
            $sql .=" AND subjects.id=".$subject_id;
        }
    
        $query = $this->db->query($sql);
    
        $subjects = $query->result();
    
        if (!empty($subjects)) {
            $count = 1;
            $append_sql = "";
            $append_param = "";
            foreach ($subjects as $subject_key => $subject_value) {
                $append_param .= ",alternate_student_subject_attendances_" . $count . ".attendence_type_id as attendence_type_id_" . $count;
                $append_sql .= " LEFT JOIN alternate_student_subject_attendances as alternate_student_subject_attendances_" . $count . " on alternate_student_subject_attendances_" . $count . ".student_session_id=student_session.id and alternate_student_subject_attendances_" . $count . ".subject_timetable_id=" . $this->db->escape($subject_value->id) . " and alternate_student_subject_attendances_" . $count . ".date=" . $this->db->escape($date);
                $count++;
            }
            $sql_student_record = "SELECT students.id,students.firstname,students.middlename,students.lastname,student_session.subject_id as session_subject,student_session.is_compulsory,students.admission_no " . $append_param . " FROM `students` INNER JOIN student_session on students.id=student_session.student_id and student_session.class_id=" . $this->db->escape($class_id) . " AND student_session.section_id=" . $this->db->escape($section_id) . " AND student_session.session_id=" . $this->db->escape($this->current_session) . $append_sql;
    
            $query = $this->db->query($sql_student_record);
            $student_record = $query->result();
            return json_encode(array('subjects' => $subjects, 'student_record' => $student_record));
        }
    
        return false;
    }


    function get_subject($id){
        $sql2 = "SELECT  subject_group_subject_id from subject_timetable where id = '".$id."'";
        $query = $this->db->query($sql2);
        return $query->row_array();
    }

    public function getStudentMontlyAttendence($class_id, $section_id, $from_date, $to_date, $student_id,$subject_id)
    {

        $student_array = array();

        $student_array['students_attendances'] = array();
   
             for ($i = strtotime($from_date); $i <= strtotime($to_date); $i+=86400) {

             $date_no=$date = date('d',$i);
            $date = date('Y-m-d',$i);
            $day = date('l', strtotime($date));

            $students_time_table = $this->searchByStudentAttendanceByDate($class_id, $section_id, $day, $date, $student_id,$subject_id);
            $a                   = array();
            $a['date']           = $this->customlib->dateformat($date);
            $a['day']            = $day;
            $a['subjects']       = array();
            $a['attendances']    = array();

            if (!empty($students_time_table)) {
                $students_time_table = json_decode($students_time_table);

                $a['subjects'] = ($students_time_table->subjects);
                foreach ($students_time_table->student_record as $students_time_table_key => $students_time_table_value) {
                    $a['attendances'] = ($students_time_table->student_record[$students_time_table_key]);
                }
            }
            $student_array['students_attendances'][$date_no] = $a;
        }
        return $student_array;
    }

    public function searchByStudentAttendanceByDate($class_id, $section_id, $day, $date, $student_id,$subject_id)
    {

        $sql = "SELECT subject_timetable.*,subjects.id as `subject_id`,subjects.name,subjects.code,subjects.type FROM `subject_timetable` INNER JOIN subject_group_subjects on subject_group_subjects.id=subject_timetable.subject_group_subject_id INNER JOIN subjects on subjects.id=subject_group_subjects.subject_id WHERE subject_timetable.class_id=" . $this->db->escape($class_id) . " AND subject_timetable.section_id=" . $this->db->escape($section_id) . " and subject_timetable.session_id=" . $this->db->escape($this->current_session) . " and subject_timetable.day=" . $this->db->escape($day);
         if($subject_id !=""){
            $sql .=" AND subjects.id=".$subject_id;
        }
       

        $query    = $this->db->query($sql);
        $subjects = $query->result();

        if (!empty($subjects)) {
            $count        = 1;
            $append_sql   = "";
            $append_param = "";
            foreach ($subjects as $subject_key => $subject_value) {
                $append_param .= ",student_subject_attendances_" . $count . ".attendence_type_id as attendence_type_id_" . $count;
                $append_sql .= " LEFT JOIN student_subject_attendances as student_subject_attendances_" . $count . " on  student_subject_attendances_" . $count . ".student_session_id=student_session.id and student_subject_attendances_" . $count . ".subject_timetable_id=" . $this->db->escape($subject_value->id) . " and student_subject_attendances_" . $count . ".date=" . $this->db->escape($date);
                $count++;
            }
            $sql_student_record = "SELECT student_session.subject_id as session_subject,student_session.is_compulsory,students.id,students.firstname" . $append_param . " FROM `students` INNER JOIN student_session on students.id=student_session.student_id and student_session.class_id=" . $this->db->escape($class_id) . " AND student_session.section_id=" . $this->db->escape($section_id) . " AND student_session.session_id=" . $this->db->escape($this->current_session) . $append_sql . " WHERE students.id=" . $student_id;
            $query              = $this->db->query($sql_student_record);
            $student_record     = $query->result();
            return json_encode(array('subjects' => $subjects, 'student_record' => $student_record));
        }

        return false;
    }

    // public function studentAttendanceByDate($class_id, $section_id, $day, $date, $student_session_id)
    // {
    //     $sql        = "SELECT subject_timetable.*,subject_group_subjects.subject_group_id,subjects.id as `subject_id`,subjects.name,subjects.code,subjects.type,student_subject_attendances.student_session_id,student_subject_attendances.attendence_type_id,student_subject_attendances.date,student_subject_attendances.remark,student_subject_attendances.id as `student_subject_attendance_id`,student_subject_attendances.date  FROM `subject_timetable` INNER JOIN subject_group_subjects on subject_group_subjects.id = subject_timetable.subject_group_subject_id and subject_group_subjects.session_id=" . $this->current_session . " INNER JOIN subjects on subjects.id=subject_group_subjects.subject_id LEFT JOIN student_subject_attendances on student_subject_attendances.subject_timetable_id=subject_timetable.id and student_subject_attendances.student_session_id=" . $this->db->escape($student_session_id) . " WHERE subject_timetable.class_id=" . $this->db->escape($class_id) . " AND subject_timetable.section_id=" . $this->db->escape($section_id) . " and subject_timetable.day=" . $this->db->escape($day) . "and student_subject_attendances.date=" . $this->db->escape($date);
    //     $query      = $this->db->query($sql);
    //     $attendance = $query->result();
    //     return $attendance;
    // }
    
    public function studentAttendanceByDate($class_id, $section_id, $day, $date, $student_session_id)
    {
        $sql = "SELECT subject_timetable.*,subject_group_subjects.subject_group_id,subjects.id as `subject_id`,subjects.name,subjects.code,subjects.type,student_subject_attendances.student_session_id,student_subject_attendances.attendence_type_id,student_subject_attendances.date,student_subject_attendances.remark,student_subject_attendances.id as `student_subject_attendance_id`,student_subject_attendances.date  FROM `subject_timetable` INNER JOIN subject_group_subjects on subject_group_subjects.id = subject_timetable.subject_group_subject_id and subject_group_subjects.session_id=" . $this->current_session . " INNER JOIN subjects on subjects.id=subject_group_subjects.subject_id LEFT JOIN student_subject_attendances on student_subject_attendances.subject_timetable_id=subject_timetable.id and student_subject_attendances.student_session_id=" . $this->db->escape($student_session_id) . " WHERE subject_timetable.section_id=" . $this->db->escape($section_id) . " and subject_timetable.day=" . $this->db->escape($day) . "and student_subject_attendances.date=" . $this->db->escape($date);
        $query      = $this->db->query($sql);
        $attendance = $query->result();
        return $attendance;
        
        //subject_timetable.class_id=" . $this->db->escape($class_id) . " AND
    }
    

    public function getStudentsMontlyAttendence($class_id, $section_id,$total_days,$subject_id)
    {

        $student_array = array();
        $student_array['class_students'] = $this->student_model->searchByClassSectionWithSession($class_id, $section_id);

        $student_array['students_attendances'] = array();

        for ($i = 0; $i < count($total_days); $i++) {
            $date_no= date('d',strtotime($total_days[$i]));
            $date = date('Y-m-d',strtotime($total_days[$i]));
            $day = date('l', strtotime($date));
            $students_time_table = $this->searchByStudentsAttendanceByDate($class_id, $section_id, $day, $date,$subject_id);
            $a             = array();
            $a['date']     = $date;
            $a['day']      = $day;
            $a['subjects'] = array();
            $a['students'] = array();

            if (!empty($students_time_table)) {
                $students_time_table = json_decode($students_time_table);

                $a['subjects'] = ($students_time_table->subjects);
                foreach ($students_time_table->student_record as $students_time_table_key => $students_time_table_value) {
                    $a['students'][$students_time_table_value->id] = ($students_time_table->student_record[$students_time_table_key]);
                }
            }
            $student_array['students_attendances'][$date] = $a;
        }

        return $student_array;
    }


    public function searchByStudentsAttendanceByDate($class_id, $section_id, $day, $date,$subject_id)
    {

        $sql = "SELECT subject_timetable.*,subjects.id as `subject_id`,subjects.name,subjects.code,subjects.type FROM `subject_timetable` INNER JOIN subject_group_subjects on subject_group_subjects.id=subject_timetable.subject_group_subject_id INNER JOIN subjects on subjects.id=subject_group_subjects.subject_id WHERE subject_timetable.class_id=" . $this->db->escape($class_id) . " AND subject_timetable.section_id=" . $this->db->escape($section_id) . " and subject_timetable.session_id=" . $this->db->escape($this->current_session) . " and subject_timetable.day=" . $this->db->escape($day);
        if($subject_id !=""){
            $sql .=" AND subjects.id=".$subject_id;
        }
       

        $query = $this->db->query($sql);

        $subjects = $query->result();

        if (!empty($subjects)) {
            $count        = 1;
            $append_sql   = "";
            $append_param = "";
            foreach ($subjects as $subject_key => $subject_value) {
                $append_param .= ",student_subject_attendances_" . $count . ".attendence_type_id as attendence_type_id_" . $count;
                $append_sql .= " LEFT JOIN student_subject_attendances as student_subject_attendances_" . $count . " on  student_subject_attendances_" . $count . ".student_session_id=student_session.id and student_subject_attendances_" . $count . ".subject_timetable_id=" . $this->db->escape($subject_value->id) . " and student_subject_attendances_" . $count . ".date=" . $this->db->escape($date);
                $count++;
            }
            $sql_student_record = "SELECT students.id,students.firstname,students.middlename,students.lastname,student_session.subject_id as session_subject,student_session.is_compulsory,students.admission_no " . $append_param . " FROM `students` INNER JOIN student_session on students.id=student_session.student_id and student_session.class_id=" . $this->db->escape($class_id) . " AND student_session.section_id=" . $this->db->escape($section_id) . " AND student_session.session_id=" . $this->db->escape($this->current_session) . $append_sql;

            $query              = $this->db->query($sql_student_record);
            $student_record     = $query->result();
            return json_encode(array('subjects' => $subjects, 'student_record' => $student_record));
        }

        return false;
    }

    public function attendanceYearCount()
    {

        $query = $this->db->select("distinct year(date) as year")->get("student_subject_attendances");

        return $query->result_array();
    }

    public function is_biometricAttendence()
    {

        $this->db->select('sch_settings.id,sch_settings.biometric,sch_settings.attendence_type,sch_settings.is_rtl,sch_settings.timezone,
          sch_settings.name,sch_settings.email,sch_settings.biometric,sch_settings.biometric_device,sch_settings.phone,languages.language,
          sch_settings.address,sch_settings.dise_code,sch_settings.date_format,sch_settings.currency,sch_settings.currency_symbol,sch_settings.start_month,sch_settings.session_id,sch_settings.image,sch_settings.theme,sessions.session'
        );

        $this->db->from('sch_settings');
        $this->db->join('sessions', 'sessions.id = sch_settings.session_id');
        $this->db->join('languages', 'languages.id = sch_settings.lang_id');
        $this->db->order_by('sch_settings.id');
        $query  = $this->db->get();
        $result = $query->row();

        if ($result->biometric) {
            return true;
        }

        return false;
    }
    

    public function getExtraAttendanceStats($class_id, $section_id, $date) {
    if (empty($class_id) || empty($section_id) || empty($date)) {
        return array();
    }

    // Parse date range
    $dates = explode(' - ', $date);
    $start_date = date('Y-m-d', strtotime($dates[0]));
    $end_date = isset($dates[1]) ? date('Y-m-d', strtotime($dates[1])) : $start_date;

    $sql = "SELECT DISTINCT 
        s.id,
        CONCAT(COALESCE(s.firstname, ''), ' ', COALESCE(s.lastname, '')) as name,
        s.father_name,
        s.mobileno,
        ss.id as student_session_id,
        (
            (SELECT COUNT(*) FROM extra_student_attendances 
             WHERE student_session_id = ss.id 
             AND attendence_type_id = 1 
             AND date BETWEEN ? AND ?) +
            (SELECT COUNT(*) FROM extra_student_attendances 
             WHERE student_session_id = ss.id 
             AND attendence_type_id = 4 
             AND date BETWEEN ? AND ?) +
            (SELECT COUNT(*) FROM extra_student_attendances 
             WHERE student_session_id = ss.id 
             AND attendence_type_id = 6 
             AND date BETWEEN ? AND ?)
        ) as total_classes,
        (SELECT COUNT(*) FROM extra_student_attendances 
         WHERE student_session_id = ss.id 
         AND attendence_type_id = 1 
         AND date BETWEEN ? AND ?) as present_count,
        (SELECT COUNT(*) FROM extra_student_attendances 
         WHERE student_session_id = ss.id 
         AND attendence_type_id = 4 
         AND date BETWEEN ? AND ?) as absent_count,
        (SELECT COUNT(*) FROM extra_student_attendances 
         WHERE student_session_id = ss.id 
         AND attendence_type_id = 6 
         AND date BETWEEN ? AND ?) as halfday_count,
        CASE 
            WHEN (
                (SELECT COUNT(*) FROM extra_student_attendances 
                 WHERE student_session_id = ss.id 
                 AND attendence_type_id IN (1, 4, 6)
                 AND date BETWEEN ? AND ?)
            ) > 0 
            THEN 
                ROUND(
                    (
                        (SELECT COUNT(*) FROM extra_student_attendances 
                         WHERE student_session_id = ss.id 
                         AND attendence_type_id IN (1, 6)
                         AND date BETWEEN ? AND ?) * 100.0
                    ) / 
                    (
                        (SELECT COUNT(*) FROM extra_student_attendances 
                         WHERE student_session_id = ss.id 
                         AND attendence_type_id IN (1, 4, 6)
                         AND date BETWEEN ? AND ?)
                    ),
                    2
                )
            ELSE 0 
        END as attendance_percentage
    FROM 
        students s
        JOIN student_session ss ON ss.student_id = s.id
        AND ss.class_id = ?
        AND ss.section_id = ?
        AND ss.session_id = ?
    WHERE 
        s.is_active = 'yes'
    ORDER BY 
        name ASC";

    $params = array(
        $start_date, $end_date,  // total classes (present)
        $start_date, $end_date,  // total classes (absent)
        $start_date, $end_date,  // total classes (half day)
        $start_date, $end_date,  // present count
        $start_date, $end_date,  // absent count
        $start_date, $end_date,  // halfday count
        $start_date, $end_date,  // total classes check for percentage
        $start_date, $end_date,  // numerator (present + half day)
        $start_date, $end_date,  // denominator (total classes)
        $class_id,
        $section_id,
        $this->current_session
    );

    $query = $this->db->query($sql, $params);
    return $query->result();
}

public function getEventAttendanceStats($class_id, $section_id, $start_date, $end_date) {
    $sql = "SELECT 
        s.id,
        CONCAT(COALESCE(s.firstname, ''), ' ', COALESCE(s.lastname, '')) as name,
        s.father_name,
        s.mobileno,
        ss.id as student_session_id,
        COALESCE(
            SUM(
                CASE
                    WHEN eda.attendance_type = '' OR eda.attendance_type IS NULL OR eda.attendance_type = 'Absent' THEN 0
                    WHEN eda.attendance_type REGEXP '^[0-9]+$' THEN CAST(eda.attendance_type AS DECIMAL)
                    ELSE 0
                END
            ), 
            0
        ) as present_count
    FROM 
        students s
        INNER JOIN student_session ss ON ss.student_id = s.id 
            AND ss.class_id = ? 
            AND ss.section_id = ? 
            AND ss.session_id = ?
        LEFT JOIN event_day_attendances eda ON eda.student_session_id = ss.id 
            AND eda.date BETWEEN ? AND ?
    WHERE 
        s.is_active = 'yes'
    GROUP BY 
        s.id, ss.id, s.firstname, s.lastname, s.father_name, s.mobileno
    ORDER BY 
        name ASC";

    $params = array(
        $class_id,
        $section_id, 
        $this->current_session,
        $start_date,
        $end_date
    );

    $query = $this->db->query($sql, $params);
    return $query->result();
}

public function getOriginalClassSection($student_session_id) 
{
    $sql = "SELECT 
        c.class as original_class,
        s.section as original_section
        FROM student_session ss
        LEFT JOIN classes c ON c.id = ss.class_id
        LEFT JOIN sections s ON s.id = ss.section_id
        WHERE ss.id = " . $this->db->escape($student_session_id);

    $query = $this->db->query($sql);
    return $query->row_array();
}

}