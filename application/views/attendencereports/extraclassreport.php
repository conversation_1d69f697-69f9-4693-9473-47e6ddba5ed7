<div class="content-wrapper" style="min-height: 946px;">
<!-- Content Header (Page header) -->
<style>
    
    .broodle-hidden {
            display: none !important;
    }
    
    #printButtonContainer {
        text-align: right;
        margin: 20px 0;
    }
    #printButton {
        margin-left: auto;
    }

    .table-responsive { 
        overflow-x: auto; 
    }
</style>

<section class="content-header">
   <h1><i class="fa fa-calendar-check-o"></i> <?php echo $this->lang->line('attendance'); ?> <small><?php echo $this->lang->line('by_date1'); ?></small></h1>
</section>
<!-- Main content -->
<section class="content">
   <?php $this->load->view('attendencereports/_attendance');?>
   <div class="row">
   <div class="col-md-12">
      <div class="box removeboxmius">
         <div class="box-header ptbnull"></div>
         <div class="box-header with-border">
            <h3 class="box-title"><i class="fa fa-search"></i> <?php echo $this->lang->line('select_criteria'); ?></h3>
         </div>
         <form id='form1' action="<?php echo site_url('attendencereports/extraclassreport') ?>"  method="post" accept-charset="utf-8">
            <div class="box-body">
               <?php
                  if ($this->session->flashdata('msg')) {
                      echo $this->session->flashdata('msg');
                      $this->session->unset_userdata('msg');
                  }
                  ?>
               <?php echo $this->customlib->getCSRF(); ?>
               <div class="row">
                  <div class="col-md-3">
                     <div class="form-group">
                        <label for="exampleInputEmail1"><?php echo $this->lang->line('class'); ?></label><small class="req"> *</small>
                        <select autofocus="" id="class_id" name="class_id" class="form-control" >
                           <option value=""><?php echo $this->lang->line('select'); ?></option>
                           <?php
                              foreach ($classlist as $class) {
                                  ?>
                           <option value="<?php echo $class['id'] ?>" <?php
                              if (set_value('class_id') == $class['id']) {
                                      echo "selected =selected";
                                  }
                                  ?>><?php echo $class['class'] ?></option>
                           <?php
                              $count++;
                              }
                              ?>
                        </select>
                        <span class="text-danger"><?php echo form_error('class_id'); ?></span>
                     </div>
                  </div>
                  <div class="col-md-3">
                     <div class="form-group">
                        <label for="exampleInputEmail1"><?php echo $this->lang->line('section'); ?></label><small class="req"> *</small>
                        <select  id="section_id" name="section_id" class="form-control" >
                           <option value=""><?php echo $this->lang->line('select'); ?></option>
                        </select>
                        <span class="text-danger"><?php echo form_error('section_id'); ?></span>
                     </div>
                  </div>
                  <div class="col-md-3">
                     <div class="form-group">
                        <label for="exampleInputEmail1">
                        <?php echo $this->lang->line('month') ?>
                        </label><small class="req"> *</small>
                        <input type="text" name="month" class="form-control" value="<?php echo (isset($month_input))?$month_input:'';?>" id="range_picker"> 
                        <!--<select  id="month" name="month" class="form-control" >
                           <option value=""><?php echo $this->lang->line('select'); ?></option>
                           <?php
                              foreach ($monthlist as $m_key => $month) {
                                  ?>
                               <option value="<?php echo $m_key ?>" <?php echo set_select('month', $m_key, set_value('month')) ?>><?php echo $month; ?></option>
                               <?php
                              }
                              ?>
                           </select>-->
                        <span class="text-danger"><?php echo form_error('month'); ?></span>
                     </div>
                  </div>
                  <div class="col-md-12">
                     <div class="form-group">
                        <button type="submit" name="search" value="search" class="btn btn-primary btn-sm pull-right checkbox-toggle"><i class="fa fa-search"></i> <?php echo $this->lang->line('search'); ?></button>
                     </div>
                  </div>
               </div>
            </div>
         </form>
         <?php
            if (isset($resultlist)) {
                ?>
         <div class="">
            <div class="box-header ptbnull"></div>
            <div class="box-header with-border">
               <h3 class="box-title"><i class="fa fa-users"></i> <?php echo $this->lang->line('extraclassreport'); ?> </h3>
               <div class="box-tools pull-right">
               </div>
            </div>
            <div class="box-body">
               <?php
                  //echo $no_of_days; die;
                     if (!empty($resultlist)) {
                             ?>
               <div class="download_label"><?php echo $this->lang->line('extraclassreport'); ?> </div>
<div id="printButtonContainer">
    <button id="printButton" class="btn btn-primary"><i class="fa fa-print"></i> Print Report</button>
</div>
               <div class="table-responsive">
                  <table class="table table-hover table stripped attendance_table" id="extraClassTable">
                     <thead>
                        <tr>
                           <th>S.No</th>
                           <th>Student Name</th>
                           <th>Father Name</th>
                           <th>Mobile Number</th>
                           <th>Total Classes</th>
                           <th>P</th>
                           <th>A</th>
                           <th>D</th>
                           <th>%</th>
                           <th>Remarks</th>
                        </tr>
                     </thead>
                     <tbody>
                        <?php foreach($resultlist as $rKey=>$r){?>
                            <tr>
                                <td><?php echo $rKey+1; ?></td>
                                <td><?php echo $r->name; ?></td>
                                <td><?php echo $r->father_name; ?></td>
                                <td><?php echo $r->mobileno; ?></td>
                                <td><?php echo $r->total_classes; ?></td>
                                <td><?php echo $r->present_count; ?></td>
                                <td><?php echo $r->absent_count; ?></td>
                                <td><?php echo $r->halfday_count; ?></td>
                                <td><?php echo $r->attendance_percentage; ?></td>
                                <td></td>
                            </tr>
                        <?php } ?>
                     </tbody>
                  </table>
               </div>
               <?php
                  } else {
                          ?>
               <div class="alert alert-info">No student admitted in this Class-Section</div>
               <?php
                  }
                      ?>
            </div>
         </div>
      </div>
      <?php
         }
         ?>
</section>
</div>
<?php
   function getAttendance($array, $student_session_id)
   {
       if (!empty($array)) {
           return $array[$student_session_id];
       }
   }
   
   function getattendencetype($attendencetype, $find)
   {
       foreach ($attendencetype as $attendencetype_key => $attendencetype_value) {
           if ($attendencetype_value['id'] == $find) {
               return $attendencetype_value['key_value'];
           }
       }
       return false;
   }
   ?>
<script type="text/javascript">
   $(document).ready(function () {
       var section_id_post = "<?php echo set_value('section_id'); ?>";
       var class_id_post = "<?php echo set_value('class_id'); ?>";
       var date_post = "<?php echo set_value('date'); ?>";

       populateSection(section_id_post, class_id_post);
   
       function populateSection(section_id_post, class_id_post) {
           if (section_id_post != "" && class_id_post != "") {
   
               $('#section_id').html("");
   
               var div_data = '<option value=""><?php echo $this->lang->line('select'); ?></option>';
               $.ajax({
                   type: "GET",
                   url: baseurl + "sections/getByClass",
                   data: {'class_id': class_id_post},
                   dataType: "json",
                   success: function (data) {
                       $.each(data, function (i, obj)
                       {
                           var select = "";
                           if (section_id_post == obj.section_id) {
                               var select = "selected=selected";
                           }
                           div_data += "<option value=" + obj.section_id + " " + select + ">" + obj.section + "</option>";
                       });
                       $('#section_id').append(div_data);
                   }
               });
           }
       }
   
       $(document).on('change', '#class_id', function (e) {
           $('#section_id').html("");
           var class_id = $(this).val();
           var div_data = '<option value=""><?php echo $this->lang->line('select'); ?></option>';
           var url = "";
           $.ajax({
               type: "GET",
               url: baseurl + "sections/getByClass",
               data: {'class_id': class_id},
               dataType: "json",
               success: function (data) {
                   $.each(data, function (i, obj)
                   {
                       div_data += "<option value=" + obj.section_id + ">" + obj.section + "</option>";
                   });
                   $('#section_id').append(div_data);
               }
           });
       });
   
   
       $(document).on('change', '#section_id', function (e) {
           let class_id = $('#class_id').val();
           let section_id = $(this).val();
   
       });
   });
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTables with sorting enabled
    var table = $('#extraClassTable').DataTable({
        "order": [[1, "asc"]], // Sort by student name column (index 1)
        "paging": false,
        "info": false,
        "searching": false,
        "ordering": true,
        "lengthChange": false,
        "autoWidth": true,
        "columnDefs": [
            { "orderable": false, "targets": [0, 9] }, // Disable sorting on S.No and Remarks columns
            { "width": "1%", "targets": 0 },
            { "type": "numeric", "targets": [4, 5, 6, 7, 8] } // Specify numeric sorting for number columns
        ],
        "drawCallback": function(settings) {

            this.api().column(0).nodes().each(function(cell, i) {
                cell.innerHTML = i + 1;
            });
        }
    });

    $('.example thead th').css('cursor', 'pointer');

    document.getElementById('printButton').addEventListener('click', function() {
        var fromDate = document.querySelector('[name="month"]').value.split(' - ')[0];
        var toDate = document.querySelector('[name="month"]').value.split(' - ')[1];
        var className = document.querySelector('#class_id option:checked').text;
        var sectionName = document.querySelector('#section_id option:checked').text;
        var schoolName = "<?php echo $this->customlib->getSchoolName(); ?>";

        // Force table redraw
        table.draw(false);

        // Create print window
        var printWindow = window.open('', '_blank', 'height=600,width=800');
        
        printWindow.document.write(`
            <html>
            <head>
                <title>Extra Attendance Report ${className} (${sectionName})</title>
                <style>
                    @page { size: landscape; }
                    body { font-family: Verdana, sans-serif; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .header img { max-width: 100%; height: auto; }
                    .title { font-size: 16px; font-weight: bold; margin: 10px 0; }
                    .info { margin-bottom: 20px; }
                    table { 
                        width: 100% !important; 
                        margin: 0 auto; 
                        border-collapse: collapse; 
                    }
                    th, td { 
                        border: 1px solid #000; 
                        padding: 5px; 
                        text-align: left; 
                        font-size: 10px; 
                        white-space: nowrap; 
                    }
                    th { background-color: #f2f2f2; }
                    .table-row { 
                        display: flex; 
                        justify-content: space-between; 
                    }
                    .left-column { text-align: left; }
                    .right-column { text-align: right; }
                </style>
            </head>
            <body>
                <div class="header">
                    <img src="https://erp.sxcjpr.edu.in/header.png" alt="School Header">
                    <div class="title">EXTRA CLASS ATTENDANCE REPORT</div>
                </div>
                <div class="info">
                    <div class="table-row">
                        <div class="left-column"><strong>From Date:</strong> ${fromDate}</div>
                        <div class="right-column"><strong>To Date:</strong> ${toDate}</div>
                    </div>
                    <p><strong>Course Name:</strong> ${className} (${sectionName})</p>
                </div>
                ${document.querySelector('.table-responsive').innerHTML}
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.onload = function() {
            printWindow.print();
            printWindow.close();
        };
    });
});
</script>

<div id="printableArea" style="display: none;">
    <div id="printHeader">
        <img src="https://erp.sxcjpr.edu.in/header.png" alt="School Header">
        <h2 id="printSchoolName"></h2>
        <div id="printTitle">EXTRA CLASS ATTENDANCE REPORT</div>
    </div>
    <div id="printInfo">
        <p><strong>From Date:</strong> <span id="printFromDate"></span></p>
        <p><strong>To Date:</strong> <span id="printToDate"></span></p>
        <p><strong>Class with Section:</strong> <span id="printClassSection"></span></p>
    </div>
    <div id="printTableArea"></div>
</div>