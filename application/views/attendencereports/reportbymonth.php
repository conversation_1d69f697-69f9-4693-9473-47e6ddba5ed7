<div class="content-wrapper" style="min-height: 946px;">
<!-- Content Header (Page header) -->
<section class="content-header">
   <h1><i class="fa fa-calendar-check-o"></i> <?php echo $this->lang->line('attendance'); ?> <small><?php echo $this->lang->line('by_date1'); ?></small></h1>
</section>
<!-- Main content -->
<section class="content">
   <?php $this->load->view('attendencereports/_attendance');?>
   <div class="row">
   <div class="col-md-12">
      <div class="box removeboxmius">
         <div class="box-header ptbnull"></div>
         <div class="box-header with-border">
            <h3 class="box-title"><i class="fa fa-search"></i> <?php echo $this->lang->line('select_criteria'); ?></h3>
         </div>
         <form id='form1' action="<?php echo site_url('attendencereports/reportbymonth') ?>"  method="post" accept-charset="utf-8">
            <div class="box-body">
               <?php
                  if ($this->session->flashdata('msg')) {
                      echo $this->session->flashdata('msg');
                      $this->session->unset_userdata('msg');
                  }
                  ?>
               <?php echo $this->customlib->getCSRF(); ?>
               <div class="row">
                  <div class="col-md-3">
                     <div class="form-group">
                        <label for="exampleInputEmail1"><?php echo $this->lang->line('class'); ?></label><small class="req"> *</small>
                        <select autofocus="" id="class_id" name="class_id" class="form-control" >
                           <option value=""><?php echo $this->lang->line('select'); ?></option>
                           <?php
                              foreach ($classlist as $class) {
                                  ?>
                           <option value="<?php echo $class['id'] ?>" <?php
                              if (set_value('class_id') == $class['id']) {
                                      echo "selected =selected";
                                  }
                                  ?>><?php echo $class['class'] ?></option>
                           <?php
                              $count++;
                              }
                              ?>
                        </select>
                        <span class="text-danger"><?php echo form_error('class_id'); ?></span>
                     </div>
                  </div>
                  <div class="col-md-3">
                     <div class="form-group">
                        <label for="exampleInputEmail1"><?php echo $this->lang->line('section'); ?></label><small class="req"> *</small>
                        <select  id="section_id" name="section_id" class="form-control" >
                           <option value=""><?php echo $this->lang->line('select'); ?></option>
                        </select>
                        <span class="text-danger"><?php echo form_error('section_id'); ?></span>
                     </div>
                  </div>
                  <div class="col-md-3">
                     <div class="form-group">
                        <label for="exampleInputEmail1">
                        <?php echo $this->lang->line('month') ?>
                        </label><small class="req"> *</small>
                        <input type="text" name="month" class="form-control" value="<?php echo (isset($month_input))?$month_input:'';?>" id="range_picker"> 
                        <!--<select  id="month" name="month" class="form-control" >
                           <option value=""><?php echo $this->lang->line('select'); ?></option>
                           <?php
                              foreach ($monthlist as $m_key => $month) {
                                  ?>
                               <option value="<?php echo $m_key ?>" <?php echo set_select('month', $m_key, set_value('month')) ?>><?php echo $month; ?></option>
                               <?php
                              }
                              ?>
                           </select>-->
                        <span class="text-danger"><?php echo form_error('month'); ?></span>
                     </div>
                  </div>
                  <div class="col-md-3">
                     <div class="form-group">
                        <label for="exampleInputEmail1">
                        <?php echo $this->lang->line('subject') ?>
                        </label><small class="req"> *</small>
                        <select  id="subject_id" name="subject_id" class="form-control" >
                           <option value=""><?php echo $this->lang->line('select'); ?></option>
                        </select>
                     </div>
                  </div>
                  <div class="col-md-12">
                     <div class="form-group">
                        <button type="submit" name="search" value="search" class="btn btn-primary btn-sm pull-right checkbox-toggle"><i class="fa fa-search"></i> <?php echo $this->lang->line('search'); ?></button>
                     </div>
                  </div>
               </div>
            </div>
         </form>
         <?php
            if (isset($resultlist)) {
                ?>
         <div class="">
            <div class="box-header ptbnull"></div>
            <div class="box-header with-border">
               <h3 class="box-title"><i class="fa fa-users"></i> <?php echo $this->lang->line('student_period_attendance'); ?>&nbsp;<?php echo $subject->name; ?></h3>
               <div class="box-tools pull-right">
                  <button type="button" class="btn btn-primary btn-sm" onclick="printCustomReport()"><i class="fa fa-print"></i> Print Report</button>
               </div>
            </div>
            <div class="box-body">
               <?php
                  //echo $no_of_days; die;
                     if (!empty($resultlist)) {
                             ?>
               <div class="download_label"><?php echo $this->lang->line('student_period_attendance'); ?> &nbsp;<?php echo $subject->name; ?> </div>
               <div class="table-responsive">
                  <table class="table table-hover table stripped attendance_table example" >
                     <thead>
                        <tr>
                           <th>Student</th>
                           <th>%</th>
                           <th>P</th>
                           <th>A</th>
                           <th>D</th>
                           <?php
                              for ($i = 0; $i < count($no_of_days); $i++) { 
                              ?>
                           <th class="text text-center">
                              <?php
                                 echo $no_of_days[$i];
                                 ?>
                           </th>
                           <?php } ?>
                        </tr>
                     </thead>
                     <tbody>
                        <?php
                           if (!empty($resultlist['class_students'])) {
                                       foreach ($resultlist['class_students'] as $student_key => $student_value) {
                                      
                                        $pCount = 0;
                                        $present = 0;
                                        $absent = 0;
                                        $half = 0;
                                           ?>
                        <tr>
                           <td>
                              <?php echo $this->customlib->getFullName($student_value['firstname'], $student_value['middlename'], $student_value['lastname'], $sch_setting->middlename, $sch_setting->lastname) . ' (' . $student_value['admission_no'] . ')'; ?>
                           </td>
                           <?php 
                              for ($i = 0; $i < count($no_of_days); $i++) { 
                                 $x =  $no_of_days[$i];
                                  if (!empty($resultlist['students_attendances'][$x]['subjects'])){
                                      $students_attendance_list = getAttendance($resultlist['students_attendances'][$x]['students'], $student_value['id']);
                              
                                      $count = 1;
                                      foreach ($resultlist['students_attendances'][$x]['subjects'] as $subject_loop_key => $subject_loop_value) {
                                          if ($students_attendance_list->{"attendence_type_id_" . $count} == "") {
                              
                                          }else{
                                              $att = getattendencetype($attendencetypeslist, $students_attendance_list->{"attendence_type_id_" . $count});
                                              if(strip_tags($att) == 'P'){
                                                  //$pCount++;
                                                  $present++;
                                              }
                                              if(strip_tags($att) == 'A'){
                                                  $absent++;
                                              }
                                              if(strip_tags($att) == 'D'){
                                                  //$pCount++;
                                                  $half++;
                                              }
                                          }
                                          $count++;
                                      }
                                  }
                                  
                              }
                              ?>
                           <td>
                              <span class="label label-warning text-black "><?php echo (($present+$absent)>0)?round((($present+$half)/($present+$absent))*100):0; ?></span>
                           </td>
                           <td><?php echo $present; ?></td>
                           <td><?php echo $absent; ?></td>
                           <td><?php echo $half; ?></td>
                           <?php
                              for ($i = 0; $i < count($no_of_days); $i++) {
                                  
                                 $x =  $no_of_days[$i];
                              ?>
                           <td class="text text-center">
                              <?php
                                 if (!empty($resultlist['students_attendances'][$x]['subjects'])) {
                                                         $students_attendance_list = getAttendance($resultlist['students_attendances'][$x]['students'], $student_value['id']);
                                 
                                                         $count = 1;
                                                         foreach ($resultlist['students_attendances'][$x]['subjects'] as $subject_loop_key => $subject_loop_value) {
                                                             ?>
                              <div class="list-group">
                                 <?php
                                    if($students_attendance_list->{"attendence_type_id_" . $count} == ""){
                                    ?>
                                 <span class="label label-danger">N/A</span>
                                 <?php
                                    } else {
                                          echo " ";
                                          $att = getattendencetype($attendencetypeslist, $students_attendance_list->{"attendence_type_id_" . $count});
                                          echo $att;                      
                                    }
                                    ?>
                              </div>
                              <?php
                                 $count++;
                                 }
                                 } else { ?>
                              <span class="label label-danger">N/A</span>
                              <?php }  ?>
                           </td>
                           <?php
                              }
                                              ?>
                        </tr>
                        <?php } ?>
                     </tbody>
                  </table>
               </div>
               <?php
                  } else {
                          ?>
               <div class="alert alert-info">No student admitted in this Class-Section</div>
               <?php
                  }
                      ?>
            </div>
         </div>
      </div>
      <?php
         }}
         ?>
</section>
</div>
<?php
   function getAttendance($array, $student_session_id)
   {
       if (!empty($array)) {
           return $array[$student_session_id];
       }
   }
   
   function getattendencetype($attendencetype, $find)
   {
       foreach ($attendencetype as $attendencetype_key => $attendencetype_value) {
           if ($attendencetype_value['id'] == $find) {
               return $attendencetype_value['key_value'];
           }
       }
       return false;
   }
   ?>
<script type="text/javascript">
   $(document).ready(function () {
       var section_id_post = "<?php echo set_value('section_id'); ?>";
       var class_id_post = "<?php echo set_value('class_id'); ?>";
       var date_post = "<?php echo set_value('date'); ?>";
       var subject_id = "<?php echo set_value('subject_id'); ?>";
       var subject_timetable_id = "<?php echo set_value('subject_timetable_id', 0); ?>";
       populateSection(section_id_post, class_id_post);
       populateSubject(class_id_post,section_id_post,subject_id);
   
       function populateSection(section_id_post, class_id_post) {
           if (section_id_post != "" && class_id_post != "") {
   
               $('#section_id').html("");
   
               var div_data = '<option value=""><?php echo $this->lang->line('select'); ?></option>';
               $.ajax({
                   type: "GET",
                   url: baseurl + "sections/getByClass",
                   data: {'class_id': class_id_post},
                   dataType: "json",
                   success: function (data) {
                       $.each(data, function (i, obj)
                       {
                           var select = "";
                           if (section_id_post == obj.section_id) {
                               var select = "selected=selected";
                           }
                           div_data += "<option value=" + obj.section_id + " " + select + ">" + obj.section + "</option>";
                       });
                       $('#section_id').append(div_data);
                   }
               });
           }
       }
   
       $(document).on('change', '#class_id', function (e) {
           $('#section_id').html("");
           var class_id = $(this).val();
           var div_data = '<option value=""><?php echo $this->lang->line('select'); ?></option>';
           var url = "";
           $.ajax({
               type: "GET",
               url: baseurl + "sections/getByClass",
               data: {'class_id': class_id},
               dataType: "json",
               success: function (data) {
                   $.each(data, function (i, obj)
                   {
                       div_data += "<option value=" + obj.section_id + ">" + obj.section + "</option>";
                   });
                   $('#section_id').append(div_data);
               }
           });
       });
   
      function populateSubject(class_id_post,section_id_post,subject_id_post) {
   
           if (section_id_post != "" && class_id_post != "") {
                $('#subject_id').html("");
   
               var div_data = '<option value=""><?php echo $this->lang->line('select'); ?></option>';
               $.ajax({
                   type: "POST",
                   url: baseurl + "admin/subjectgroup/getAllSubjectByClassandSection",
               data: {'class_id': class_id_post,'section_id':section_id_post},
                   dataType: "json",
                   success: function (data) {
                       $.each(data, function (i, obj)
                       {
                           var select = "";
                           if (subject_id_post == obj.subject_id) {
                               var select = "selected=selected";
                           }
                           
                           var code ='';
                           if(obj.subject_code){
                               code = " (" + obj.subject_code + ") ";
                           }
           
                           div_data += "<option value=" + obj.subject_id + " " + select + ">" + obj.subject_name + code +"</option>";
                       });
                       $('#subject_id').append(div_data);
                   }
               });
           }
      }
   
       $(document).on('change', '#section_id', function (e) {
           $('#subject_id').html("");
           let class_id = $('#class_id').val();
           let section_id = $(this).val();
           populateSubject(class_id,section_id,0);
   
       });
   });

   function formatDate(dateStr) {
       // Extract only the day from the date string
       try {
           // Check if the date is in yyyy-mm-dd format
           if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
               return dateStr.split('-')[2]; // Return only the day
           }
           // If it's already just a day number, return as is
           return dateStr;
       } catch (e) {
           return dateStr; // Return original string if parsing fails
       }
   }

   function cleanCellContent(content) {
       // Remove any HTML tags first
       let cleanText = content.replace(/<[^>]*>/g, '').trim();
       
       // Return empty string if content is N/A or empty
       if (cleanText === 'N/A' || cleanText === '') {
           return '';
       }
       
       return cleanText;
   }

   function printCustomReport() {
       // Get the original table
       var originalTable = document.querySelector('.attendance_table');
       if (!originalTable) return;

       // Get dates from headers and sort them
       var dateHeaders = Array.from(originalTable.querySelectorAll('thead th')).slice(5);
       var dates = dateHeaders.map(th => th.textContent.trim());
       var sortedDateIndices = dates.map((date, index) => ({date, index}))
           .sort((a, b) => new Date(a.date) - new Date(b.date))
           .map(item => item.index);

       // Get class and section names
       var className = document.querySelector('#class_id option:checked').text;
       var sectionName = document.querySelector('#section_id option:checked').text;
       var fromDate = document.querySelector('[name="month"]').value.split(' - ')[0];
       var toDate = document.querySelector('[name="month"]').value.split(' - ')[1];

       // Create print content with header
       var content = `
           <!DOCTYPE html>
           <html>
           <head>
               <link rel="stylesheet" href="https://erp.sxcjpr.edu.in/backend/bootstrap/css/bootstrap.min.css">
               <style>
                   @media print {
                       @page {
                           size: landscape;
                           margin: 0.5cm;
                       }
                       * {
                           margin: 0;
                           padding: 0;
                           box-sizing: border-box;
                           font-family: Arial, Helvetica, sans-serif;
                       }
                       html, body {
                           width: 100%;
                           height: auto;
                           margin: 0;
                           padding: 0;
                           font-family: Arial, Helvetica, sans-serif;
                       }
                       #printSection { 
                           width: 100%;
                           padding: 10px;
                           margin: 0;
                           font-family: Arial, Helvetica, sans-serif;
                       }
                       table { 
                           width: 100%; 
                           border-collapse: collapse;
                           page-break-inside: auto;
                           font-family: Arial, Helvetica, sans-serif;
                       }
                       tr { 
                           page-break-inside: avoid;
                           page-break-after: auto;
                       }
                       th, td { 
                           border: 1px solid #000; 
                           padding: 5px; 
                           text-align: left; 
                           font-size: 12px;
                           font-family: Arial, Helvetica, sans-serif;
                       }
                       th { 
                           background-color: #f2f2f2; 
                           font-weight: bold;
                       }
                       .header { 
                           text-align: center; 
                           margin-bottom: 20px;
                           font-family: Arial, Helvetica, sans-serif;
                       }
                       .header img { 
                           max-width: 100%; 
                           height: auto;
                           display: block;
                           margin: 0 auto;
                       }
                       .title { 
                           font-size: 16px; 
                           font-weight: bold; 
                           margin: 10px 0;
                       }
                       .info { 
                           margin-bottom: 20px;
                       }
                       .table-row { 
                           display: flex; 
                           justify-content: space-between;
                       }
                   }
               </style>
           </head>
           <body>
               <div id="printSection">
                   <div class="header">
                       <img src="https://erp.sxcjpr.edu.in/header.png" alt="School Header">
                       <div class="title">SUBJECT WISE ATTENDANCE <br /><?php echo $subject->name; ?></div>
                   </div>
                   <div class="info">
                       <div class="table-row">
                           <div><strong>From Date:</strong> ${fromDate}</div>
                           <div><strong>To Date:</strong> ${toDate}</div>
                       </div>
                       <p><strong>Course Name:</strong> ${className} (${sectionName})</p>
                   </div>
                   <table>
                       <thead>
                           <tr>
                               <th>SN</th>
                               <th>Student Name</th>
                               ${sortedDateIndices.map(i => `<th>${formatDate(dates[i])}</th>`).join('')}
                               <th>P</th>
                               <th>A</th>
                               <th>D</th>
                               <th>%</th>
                           </tr>
                       </thead>
                       <tbody>
       `;

       // Get rows and reorder date columns
       Array.from(originalTable.querySelectorAll('tbody tr')).forEach((row, index) => {
           const cells = Array.from(row.querySelectorAll('td'));
           const fullText = cells[0].textContent.trim();
           const studentName = fullText.split('(')[0].trim();
           const presentCount = cells[2].textContent.trim();
           const absentCount = cells[3].textContent.trim();
           const halfCount = cells[4].textContent.trim();
           const percentage = cells[1].textContent.trim();
           const dateCells = cells.slice(5);
           
           content += `
               <tr>
                   <td>${index + 1}</td>
                   <td>${studentName}</td>
                   ${sortedDateIndices.map(i => {
                       const cellContent = dateCells[i] ? cleanCellContent(dateCells[i].textContent) : '';
                       return `<td>${cellContent}</td>`;
                   }).join('')}
                   <td>${presentCount}</td>
                   <td>${absentCount}</td>
                   <td>${halfCount}</td>
                   <td>${percentage}</td>
               </tr>
           `;
       });

       content += `
                       </tbody>
                   </table>
               </div>
           </body>
           </html>
       `;

       // Create a new window for printing
       var printWindow = window.open('', '_blank');
       printWindow.document.write(content);
       printWindow.document.close();

       // Wait for everything to load before printing
       printWindow.onload = function() {
           // Add a small delay to ensure styles are applied
           setTimeout(function() {
               printWindow.print();
               printWindow.close();
           }, 250);
       };
   }

   // Add event listener for the print button
   document.addEventListener('DOMContentLoaded', function() {
       const printButton = document.querySelector('.btn-primary[onclick="printCustomReport()"]');
       if (printButton) {
           printButton.onclick = function(e) {
               e.preventDefault();
               printCustomReport();
           };
       }
   });
</script>
