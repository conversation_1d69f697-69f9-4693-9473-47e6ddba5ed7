<div class="content-wrapper" style="min-height: 946px;">
<!-- Content Header (Page header) -->
<style>
    
    .broodle-hidden {
            display: none !important;
    }
    
    #printButtonContainer {
        text-align: right;
        margin: 20px 0;
    }
    #printButton {
        margin-left: auto;
    }

    }
</style>

<section class="content-header">
   <h1><i class="fa fa-calendar-check-o"></i> <?php echo $this->lang->line('attendance'); ?> <small><?php echo $this->lang->line('by_date1'); ?></small></h1>
</section>
<!-- Main content -->
<section class="content">
   <?php $this->load->view('attendencereports/_attendance');?>
   <div class="row">
   <div class="col-md-12">
      <div class="box removeboxmius">
         <div class="box-header ptbnull"></div>
         <div class="box-header with-border">
            <h3 class="box-title"><i class="fa fa-search"></i> <?php echo $this->lang->line('select_criteria'); ?></h3>
         </div>
         <form id='form1' action="<?php echo site_url('attendencereports/alternateattendancereport') ?>"  method="post" accept-charset="utf-8">
            <div class="box-body">
               <?php
                  if ($this->session->flashdata('msg')) {
                      echo $this->session->flashdata('msg');
                      $this->session->unset_userdata('msg');
                  }
                  ?>
               <?php echo $this->customlib->getCSRF(); ?>
               <div class="row">
                  <div class="col-md-3">
                     <div class="form-group">
                        <label for="exampleInputEmail1"><?php echo $this->lang->line('class'); ?></label><small class="req"> *</small>
                        <select autofocus="" id="class_id" name="class_id" class="form-control" >
                           <option value=""><?php echo $this->lang->line('select'); ?></option>
                           <?php
                              foreach ($classlist as $class) {
                                  ?>
                           <option value="<?php echo $class['id'] ?>" <?php
                              if (set_value('class_id') == $class['id']) {
                                      echo "selected =selected";
                                  }
                                  ?>><?php echo $class['class'] ?></option>
                           <?php
                              $count++;
                              }
                              ?>
                        </select>
                        <span class="text-danger"><?php echo form_error('class_id'); ?></span>
                     </div>
                  </div>
                  <div class="col-md-3">
                     <div class="form-group">
                        <label for="exampleInputEmail1"><?php echo $this->lang->line('section'); ?></label><small class="req"> *</small>
                        <select  id="section_id" name="section_id" class="form-control" >
                           <option value=""><?php echo $this->lang->line('select'); ?></option>
                        </select>
                        <span class="text-danger"><?php echo form_error('section_id'); ?></span>
                     </div>
                  </div>
                  <div class="col-md-3">
                     <div class="form-group">
                        <label for="exampleInputEmail1">
                        <?php echo $this->lang->line('month') ?>
                        </label><small class="req"> *</small>
                        <input type="text" name="month" class="form-control" value="<?php echo (isset($month_input))?$month_input:'';?>" id="range_picker"> 
                        <!--<select  id="month" name="month" class="form-control" >
                           <option value=""><?php echo $this->lang->line('select'); ?></option>
                           <?php
                              foreach ($monthlist as $m_key => $month) {
                                  ?>
                               <option value="<?php echo $m_key ?>" <?php echo set_select('month', $m_key, set_value('month')) ?>><?php echo $month; ?></option>
                               <?php
                              }
                              ?>
                           </select>-->
                        <span class="text-danger"><?php echo form_error('month'); ?></span>
                     </div>
                  </div>
                  <div class="col-md-12">
                     <div class="form-group">
                        <button type="submit" name="search" value="search" class="btn btn-primary btn-sm pull-right checkbox-toggle"><i class="fa fa-search"></i> <?php echo $this->lang->line('search'); ?></button>
                     </div>
                  </div>
               </div>
            </div>
         </form>
         <?php
            if (isset($resultlist)) {
                ?>
         <div class="">
            <div class="box-header ptbnull"></div>
            <div class="box-header with-border">
               <h3 class="box-title"><i class="fa fa-users"></i> <?php echo $this->lang->line('alternateattendancereport'); ?> </h3>
               <div class="box-tools pull-right">
               </div>
            </div>
            <div class="box-body">
               <?php
                  if (!empty($resultlist)) {
                          ?>
               <div class="download_label"><?php echo $this->lang->line('alternateattendancereport'); ?>  </div>
               
               <div id="printButtonContainer">
                    <button id="printButton" class="btn btn-primary"><i class="fa fa-print"></i> Print Report</button>
                </div>
               <div class="table-responsive">
   <table class="table table-hover table stripped attendance_table" id="attendanceTable">
   <thead>
      <tr>
         <th>S. No.</th>
         <th><?php echo $this->lang->line('student_name'); ?></th>
         <th><?php echo $this->lang->line('fathers_name'); ?></th>
         <th><?php echo $this->lang->line('mobile_number'); ?></th>
         <th class="broodle-hidden">P</th>
         <th class="broodle-hidden">A</th>
         <th class="broodle-hidden">D</th>
         <!--<th>P</th>-->
         <th>Alt. P</th>
         <th>Alt. A</th>
         <th>Alt. D</th>
         <th><?php echo $this->lang->line('present_in_class'); ?></th>
         <th><?php echo $this->lang->line('total_class'); ?></th>
         <th><?php echo $this->lang->line('percentage'); ?></th>
         <th>Remarks</th>
      </tr>
   </thead>
   <tbody>
      <?php
      if (!empty($resultlist['class_students'])) {
         $serial_no = 1;
         foreach ($resultlist['class_students'] as $student_key => $student_value) {
            $present = $absent = $half = $alt_present = $alt_absent = $alt_half = 0;

            // Regular attendance calculation
            for ($i = 0; $i < count($no_of_days); $i++) {
               $x = $no_of_days[$i];
               if (!empty($resultlist['students_attendances'][$x]['subjects'])) {
                  $students_attendance_list = getAttendance($resultlist['students_attendances'][$x]['students'], $student_value['id']);
                  $count = 1;
                  foreach ($resultlist['students_attendances'][$x]['subjects'] as $subject_loop_key => $subject_loop_value) {
                     if ($students_attendance_list->{"attendence_type_id_" . $count} != "") {
                        $att = getattendencetype($attendencetypeslist, $students_attendance_list->{"attendence_type_id_" . $count});
                        if (strip_tags($att) == 'P') {
                           $present++;
                        }
                        if (strip_tags($att) == 'A') {
                           $absent++;
                        }
                        if (strip_tags($att) == 'D') {
                           $half++;
                        }
                     }
                     $count++;
                  }
               }
            }

            // Alternate attendance calculation
            for ($i = 0; $i < count($no_of_days); $i++) {
               $x = $no_of_days[$i];
               if (!empty($alternateAttendance['students_attendances'][$x]['students'][$student_value['id']])) {
                  $alt_attendance = $alternateAttendance['students_attendances'][$x]['students'][$student_value['id']];
                  foreach ($alt_attendance as $alt_att_key => $alt_att_value) {
                     if (strpos($alt_att_key, 'attendence_type_id_') === 0) {
                        $alt_att = getattendencetype($attendencetypeslist, $alt_att_value);
                        if (strip_tags($alt_att) == 'P') {
                           $alt_present++;
                        }
                        if (strip_tags($alt_att) == 'A') {
                           $alt_absent++;
                        }
                        if (strip_tags($alt_att) == 'D') {
                           $alt_half++;
                        }
                     }
                  }
               }
            }

            // Calculate Present (P + D)
            $present_combined = $present + $half;

            // New calculations for Total Classes, Total Present, and Percentage
            // $total_classes = $present + $absent + $half + $alt_present + $alt_absent + $alt_half;
            $total_classes = $alt_present + $alt_absent + $alt_half;
            // $total_present = $present_combined + $alt_present + $alt_half;
            $total_present = $alt_present + $alt_half;
            $percentage = ($total_classes > 0) ? round(($total_present / $total_classes) * 100, 2) : 0;
      ?>
      <tr>
         <td><?php echo $serial_no; ?></td>
         <td>
            <?php echo $this->customlib->getFullName($student_value['firstname'], $student_value['middlename'], $student_value['lastname'], $sch_setting->middlename, $sch_setting->lastname); ?>
         </td>
         <td><?php echo $student_value['father_name']; ?></td>
         <td><?php echo isset($student_value['mobileno']) ? $student_value['mobileno'] : 'N/A'; ?></td>
         <td class="broodle-hidden"><?php echo $present; ?></td>
         <td class="broodle-hidden"><?php echo $absent; ?></td>
         <td class="broodle-hidden"><?php echo $half; ?></td>
         <!--<td><?php echo $present_combined; ?></td>-->
         <td><?php echo $alt_present; ?></td>
         <td><?php echo $alt_absent; ?></td>
         <td><?php echo $alt_half; ?></td>
         <td><?php echo $total_present; ?></td>
         <td><?php echo $total_classes; ?></td>
         <td><?php echo $percentage; ?>%</td>
         <td></td>
      </tr>
      <?php
         $serial_no++;
         }
      }
      ?>
   </tbody>
</table>


</div>
               <?php
                  } else {
                          ?>
               <div class="alert alert-info">No student admitted in this Class-Section</div>
               <?php
                  }
                      ?>
            </div>
         </div>
         <div id="printableArea" style="display: none;">
    <div id="printHeader">
        <img src="https://erp.sxcjpr.edu.in/header.png" alt="School Header">
        <h2 id="printSchoolName"></h2>
        <div id="printTitle">STUDENT ATTENDANCE FROM 0.00% TO 100.00%</div>
    </div>
    <div id="printInfo">
        <p><strong>From Date:</strong> <span id="printFromDate"></span></p>
        <p><strong>To Date:</strong> <span id="printToDate"></span></p>
        <p><strong>Class with Section:</strong> <span id="printClassSection"></span></p>
    </div>
    <div id="printTableArea"></div>
</div>
      </div>
      <?php
         }
         ?>
</section>
</div>
<?php
   function getAttendance($array, $student_session_id)
   {
       if (!empty($array)) {
           return $array[$student_session_id];
       }
   }
   
   function getattendencetype($attendencetype, $find)
   {
       foreach ($attendencetype as $attendencetype_key => $attendencetype_value) {
           if ($attendencetype_value['id'] == $find) {
               return $attendencetype_value['key_value'];
           }
       }
       return false;
   }
   ?>
<script type="text/javascript">
   $(document).ready(function () {
       var section_id_post = "<?php echo set_value('section_id'); ?>";
       var class_id_post = "<?php echo set_value('class_id'); ?>";
       var date_post = "<?php echo set_value('date'); ?>";
       var subject_id = "<?php echo set_value('subject_id'); ?>";
       var subject_timetable_id = "<?php echo set_value('subject_timetable_id', 0); ?>";
       populateSection(section_id_post, class_id_post);
       populateSubject(class_id_post,section_id_post,subject_id);
   
       function populateSection(section_id_post, class_id_post) {
           if (section_id_post != "" && class_id_post != "") {
   
               $('#section_id').html("");
   
               var div_data = '<option value=""><?php echo $this->lang->line('select'); ?></option>';
               $.ajax({
                   type: "GET",
                   url: baseurl + "sections/getByClass",
                   data: {'class_id': class_id_post},
                   dataType: "json",
                   success: function (data) {
                       $.each(data, function (i, obj)
                       {
                           var select = "";
                           if (section_id_post == obj.section_id) {
                               var select = "selected=selected";
                           }
                           div_data += "<option value=" + obj.section_id + " " + select + ">" + obj.section + "</option>";
                       });
                       $('#section_id').append(div_data);
                   }
               });
           }
       }
   
       $(document).on('change', '#class_id', function (e) {
           $('#section_id').html("");
           var class_id = $(this).val();
           var div_data = '<option value=""><?php echo $this->lang->line('select'); ?></option>';
           var url = "";
           $.ajax({
               type: "GET",
               url: baseurl + "sections/getByClass",
               data: {'class_id': class_id},
               dataType: "json",
               success: function (data) {
                   $.each(data, function (i, obj)
                   {
                       div_data += "<option value=" + obj.section_id + ">" + obj.section + "</option>";
                   });
                   $('#section_id').append(div_data);
               }
           });
       });
   
   function populateSubject(class_id_post,section_id_post,subject_id_post) {
   
           if (section_id_post != "" && class_id_post != "") {
                $('#subject_id').html("");
   
               var div_data = '<option value=""><?php echo $this->lang->line('select'); ?></option>';
               $.ajax({
                   type: "POST",
                   url: baseurl + "admin/subjectgroup/getAllSubjectByClassandSection",
               data: {'class_id': class_id_post,'section_id':section_id_post},
                   dataType: "json",
                   success: function (data) {
                       $.each(data, function (i, obj)
                       {
                           var select = "";
                           if (subject_id_post == obj.subject_id) {
                               var select = "selected=selected";
                           }
                           
                           var code ='';
                           if(obj.subject_code){
                               code = " (" + obj.subject_code + ") ";
                           }
           
                           div_data += "<option value=" + obj.subject_id + " " + select + ">" + obj.subject_name + code +"</option>";
                       });
                       $('#subject_id').append(div_data);
                   }
               });
           }
       }
   
       $(document).on('change', '#section_id', function (e) {
           $('#subject_id').html("");
           let class_id = $('#class_id').val();
           let section_id = $(this).val();
           populateSubject(class_id,section_id,0);
   
       });
   });
</script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTables with auto-width columns
    var table = $('#attendanceTable').DataTable({
        "order": [[1, "asc"]], // Sort by student name column (index 1) in ascending order
        "paging": false,
        "info": false,
        "searching": false,
        "ordering": true,
        "lengthChange": false,
        "autoWidth": true, // Enable auto-width for columns
        "columnDefs": [
            { "orderable": false, "targets": 0 }, // Disable sorting on the S. No. column
            { "width": "1%", "targets": 0 } // Set S. No. column to minimum width
        ],
        "drawCallback": function(settings) {
            // Update S.No. column after each draw
            this.api().column(0).nodes().each(function(cell, i) {
                cell.innerHTML = i + 1;
            });
        }
    });

    // Add CSS to the page for table layout
    var style = document.createElement('style');
    style.textContent = `
        .table-responsive { overflow-x: auto; }
        #attendanceTable {
            width: auto !important;
            margin: 0 auto;
        }
        #attendanceTable th, #attendanceTable td {
            white-space: nowrap;
            padding: 5px;
        }
    `;
    document.head.appendChild(style);

    document.getElementById('printButton').addEventListener('click', function() {
        var fromDate = document.querySelector('[name="month"]').value.split(' - ')[0];
        var toDate = document.querySelector('[name="month"]').value.split(' - ')[1];
        var className = document.querySelector('#class_id option:checked').text;
        var sectionName = document.querySelector('#section_id option:checked').text;
        var schoolName = "<?php echo $this->customlib->getSchoolName(); ?>";

        // Force a redraw of the table to ensure S.No. is updated before printing
        table.draw(false);

        // Create a new window
        var printWindow = window.open('', '_blank', 'height=600,width=800');
        
        // Write the content to the new window
        printWindow.document.write(`
            <html>
            <head>
                <title>Alternate Attendance Report ${className} (${sectionName})</title>
                <style>
                    @page {
                        size: landscape;
                    }
                    body { font-family: Verdana, sans-serif; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .header img { max-width: 100%; height: auto; }
                    .title { font-size: 16px; font-weight: bold; margin: 10px 0; }
                    .info { margin-bottom: 20px; }
                    table { width: auto !important; margin: 0 auto; border-collapse: collapse; }
                    th, td { border: 1px solid #000; padding: 5px; text-align: left; font-size: 10px; white-space: nowrap; }
                    th { background-color: #f2f2f2; }
                    .table-row { display: flex; justify-content: space-between; }
                    .left-column { text-align: left; }
                    .right-column { text-align: right; }
                    .dataTables_wrapper .dataTables_length,
                    .dataTables_wrapper .dataTables_filter,
                    .dataTables_wrapper .dataTables_info,
                    .dataTables_wrapper .dataTables_paginate {
                        display: none !important;
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <img src="https://erp.sxcjpr.edu.in/header.png" alt="School Header">
                    <div class="title">STUDENT ATTENDANCE FROM 0.00% TO 100.00%</div>
                </div>
                <div class="info">
                    <div class="table-row">
                      <div class="left-column"><strong>From Date:</strong> ${fromDate}</div>
                      <div class="right-column"><strong>To Date:</strong> ${toDate}</div>
                    </div>
                    <p><strong>Course Name:</strong> ${className} (${sectionName})</p>
                </div>
                ${document.querySelector('.table-responsive').innerHTML}
            </body>
            </html>
        `);

        // Remove hidden columns and DataTables elements
        printWindow.document.querySelectorAll('.broodle-hidden, .dataTables_wrapper .dataTables_length, .dataTables_wrapper .dataTables_filter, .dataTables_wrapper .dataTables_info, .dataTables_wrapper .dataTables_paginate').forEach(el => el.remove());

        // Close the document writing
        printWindow.document.close();

        // Wait for the image to load before printing
        printWindow.onload = function() {
            printWindow.print();
            printWindow.close();
        };
    });
});
</script>