<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300..900;1,300..900&display=swap" rel="stylesheet">

<style type="text/css">
  
  #fade {
    display: none;
    position:absolute;
    top: 0%;
    left: 0%;
    width: 100%;
    height: 100%;
    background-color: #edededcc;
    z-index: 1001;
    -moz-opacity: 0.8;
    opacity: .70;
    filter: alpha(opacity=80);
}

@media (max-width: 767px) {
    .container {
        padding-right: 0px;
        padding-left: 0px;
    }
    .reflist {
        width: 340px;
    }
}


#modal {
    display: none;
    position: absolute;
    top: 35%;
    left: 45%;
    width: 83px;
    height: 83px;
    /* padding: 24px 15px 0px; */
    /* border: 3px solid #ababab; */
    /* box-shadow: 1px 1px 10px #ababab; */
    border-radius: 20px;
    /* background-color: white; */
    z-index: 1002;
    text-align: center;
    overflow: auto;
}

body {
    background-color: #b30738;
    min-height: 100vh;
    margin: 0;
    font-family: "Rubik", sans-serif;
}

#divtoprint {
    background-color: #fcfcfc;
    border-radius: 15px;
    padding: 30px;
    margin: 20px auto;
    max-width: 1200px;
}

@media (max-width: 768px) {
  #divtoprint {
    padding: 30px 15px; /* top/bottom: 30px, left/right: 15px */
  }
}


@media (max-width: 768px) {
  .btn {
    margin-top: 20px; /* top/bottom: 30px, left/right: 15px */
  }
}

h4.entered {
    font-size: 16px; 
    font-weight: bold; 
    margin: 10px 0;
    font-family: "Rubik", sans-serif;
    
}

.pagetitleh2 {
    background: #0000;
    border-bottom: 0px solid #ffffff;
    font-family: "Rubik", sans-serif;
    padding: 8px 0px;
}

.pagetitleh3 {
    background: #0000;
    border-bottom: 0px solid #ffffff;
    font-family: "Rubik", sans-serif;
    padding: 8px 15px;
}

.printcontent {
    padding: 5px 5px 5px 5px;
}

.form-control {
    border-radius: 6px;
}

.reflist {
    list-style: none;
    padding-left: 0;
    font-family: "Rubik", sans-serif;
}

.reflist li {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
}

.statusimg {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    list-style: none;
    padding-left: 0;
    align-items: end !important;
}

.statusimg li {
    margin: 0 10px;
    text-align: center;
}

.statusimg img {
    border-radius: 8px;
    width: 150px !important;
    /*max-width: 100px;*/
    height: auto !important;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    font-weight: 500;
    font-family: "Rubik", sans-serif;
}

.footer-broodle {
    background-color: black;
    color: white;
    text-align: center;
    padding: 15px 0;
    width: 100vw; /* Use viewport width */
    font-size: 14px;
    line-height: 1.5;
    font-family: "Rubik", sans-serif;
    margin-top: 40px;
    margin-left: calc(-50vw + 50%); /* Center the full-width footer */
    margin-right: calc(-50vw + 50%);
}

.footer-broodle p {
    margin: 0;
    padding: 0 15px; /* Add some padding to text for mobile */
}

/* Print styles */
@media print {
    .footer-broodle {
        display: none;
    }
}

.printbtndrakgray {
    background-color: #A31621;
    color: white;
    border: none;
}

.printbtndrakgray:hover {
    background-color: #8a131d;
    color: white;
}

.row {
    margin-right: 0;
    margin-left: 0;
}

/* Add these styles to match the header from admission.php */
header {
    background-color: #f8f0f0; /* Light pink background */
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    font-family: "Rubik", sans-serif;
    border-radius: 15px;
    top: 20px;
    margin-bottom: 50px;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 60px; /* Adjust as needed */
    margin-right: 10px;
}

.nav-links {
    display: flex;
    align-items: center;
    font-family: "Rubik", sans-serif;
}

.nav-links a {
    text-decoration: none;
    color: #333;
    margin-left: 20px;
    font-family: "Rubik", sans-serif;
}

.nav-links .login-btn {
    background-color: #A31621; /* Dark red button */
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    font-family: "Rubik", sans-serif;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        align-items: center;
    }

    .nav-links {
        flex-direction: column;
        align-items: flex-start;
    }

    .nav-links a {
        margin: 5px 0;
    }
}

@media screen and (max-width: 700px) {
    .nav-links {
        display: none !important;
    }
    .broodle-header-text {
        font-size: 20px;
    }
}

/* Add these print-specific styles */
@media print {
  body {
    background-color: white !important;
    padding-bottom: 0 !important;
    font-family: "Rubik", sans-serif !important;
  }
  
  #divtoprint {
    background-color: white !important;
    border-radius: 0 !important;
    padding: 15px !important;
    margin: 0 !important;
    max-width: 100% !important;
    box-shadow: none !important;
  }
  
  header, .footer-broodle, .no-print {
    display: none !important;
  }
  
  /* Hide payment button, checkbox, and currency selector during printing */
  .btnprint, #paybtn, #submitbtn, #checkterm, .currency_list, 
  [data-target="#openTermsAndConditionsModal"], [name="currency"], 
  [id="checkterm"], [name="checkterm"], .payment-section {
    display: none !important;
  }
  
  /* Fix for the stretched header in print */
  #printheader {
    display: block !important;
    width: 100% !important;
    text-align: center !important;
    margin-bottom: 20px !important;
  }
  
  #printheader img {
    max-width: 100% !important;
    height: auto !important;
    width: auto !important; /* Prevent stretching */
    max-height: 100px !important; /* Control the height */
    object-fit: contain !important; /* Maintain aspect ratio */
  }
  
  .printcontent {
    padding: 0 !important;
  }
  
  /* Improve table layout for printing */
  table {
    width: 100% !important;
    border-collapse: collapse !important;
  }
  
  table, th, td {
    border: 1px solid #ddd !important;
    padding: 8px !important;
  }
  
  /* Ensure images don't get cut off */
  img {
    max-width: 100% !important;
  }
  
  /* Ensure proper page breaks */
  .page-break {
    page-break-before: always !important;
  }
  
  /* Adjust image sizes in print */
  .statusimg img {
    max-width: 80px !important;
    height: auto !important;
  }
  
  /* Adjust spacing */
  .row {
    margin-bottom: 10px !important;
  }
  
  /* Ensure text is black for better printing */
  * {
    color: black !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
  
  /* Hide the receipt text at the bottom */
  #printfooter, .receipt-text, .receipt-footer, .receipt-signature {
    display: none !important;
  }
  
  /* Additional selector to ensure the text is hidden */
  [id*="receipt"], [class*="receipt"], [id*="signature"], [class*="signature"] {
    display: none !important;
  }
}

/* Add this for the print header and footer */
#printheader, #printfooter {
  display: none;
}

#printheadid {
  display: none;
}


.reflist li {

    padding-left: 15px;
    padding-right: 15px;
}

.form-group.pull-right {
    margin-top: 15px;
}

.form-group.pull-right input[type="checkbox"] {
    margin-right: 5px;
}

/* Add spacing between terms link and button */
.form-group.pull-right a {
    margin-right: 15px;
}

/* Mobile responsive button styling */
@media (max-width: 768px) {
    /* Remove centering rules that cause misalignment */
    .form-group.pull-right {
        text-align: left !important;
        float: right !important;
        display: block;
        flex-direction: initial;
        align-items: initial;
        width: auto;
    }
    
    .form-group.pull-right #paybtn,
    .form-group.pull-right #submitbtn {
        margin-top: 10px;
    }
    
    .col-md-6:last-child {
        text-align: left;
    }
    
    /* New mobile spacing fixes */
    .btnprint .col-md-4, 
    .btnprint .col-md-8,
    .btnprint .col-md-12 {
        margin-bottom: 15px;
    }
    
    .btnprint .col-md-8 > div {
        flex-direction: column !important;
        align-items: flex-start !important;
    }
    
    .btnprint .col-md-8 > div > div {
        margin-bottom: 15px !important;
        margin-left: 0 !important;
        width: 100% !important;
    }
    
    .currency_list {
        margin-bottom: 15px !important;
    }
    
    #paybtn, #submitbtn {
        margin-top: 15px !important;
        margin-left: 0 !important;
    }
}

/* Fix for alignment issues */
.btnprint .form-inline {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
}

.btnprint .currency_list {
    display: inline-block;
    width: auto !important;
    margin-right: 10px;
    vertical-align: middle;
}

/* Fix for checkbox and button alignment */
.btnprint input[type="checkbox"] {
    vertical-align: middle;
    margin: 0 5px 0 0;
}

/* Basic details spacing */
.printcontent {
    margin-top: 15px;
}

</style>

<header>
  <a style="color: black;" href="https://sxcjpr.edu.in" target="_blank">
    <div class="logo">
      <img src="https://sxcjpr.edu.in/wp-content/uploads/2023/07/Xaviers-Jaipur-Logo.png" alt="St. Xavier's College Jaipur Logo">
      <h1 class="broodle-header-text" style="font-family: 'Times New Roman', sans-serif; font-weight: 700; letter-spacing: -1.5px; margin: 0px;">St. Xavier's College Jaipur</h1>
    </div>
  </a>
  <nav class="nav-links">
    <a href="https://sxcjpr.edu.in" target="_blank">Home</a>
    <a href="https://sxcjpr.edu.in/contact" target="_blank">Contact Us</a>
    <a href="https://erp.sxcjpr.edu.in/site/userlogin" target="_blank" class="login-btn">ERP Login</a>
  </nav>
</header>

<?php $currency_symbol = $this->customlib->getSchoolCurrencyFormat(); ?>
<div id="openTermsAndConditionsModal" class="modal fade in" role="dialog" tabindex="-1">
  <div class="modal-dialog modal-lg">

    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header modal-header-small">
        <button type="button" class="close closebtnmodal" data-dismiss="modal">&times;</button>
        <h4><?php echo $this->lang->line('online_admission_terms_conditions') ?></h4> 
      </div>
       <form action="<?php echo base_url().'welcome/checkadmissionstatus' ?>" method="post" class="onlineform" id="checkstatusform">
          <div class="modal-body">            
			<?php echo $online_admission_conditions; ?>
          </div>
          <div class="modal-footer">
          <button type="button" class="modalclosebtn btn mdbtn" data-dismiss="modal"><?php echo $this->lang->line('close');  ?></button>            
          </div>
      </form>
    </div>
  </div>
</div>

<?php
   $currency_symbol = $this->customlib->getSchoolCurrencyFormat(); 
    if ($this->session->flashdata('msg')) {
      $message = $this->session->flashdata('msg');
      echo $message;
    }

  if($student_pic!=""){
    $student_photo = base_url(). $student_pic ;
    
  }else{
    $student_photo = base_url()."uploads/student_images/no_image.png"; 
  }
  if($father_pic!=""){
    $father_photo = base_url(). $father_pic ;
    
  }else{
    $father_photo = base_url()."uploads/student_images/no_image.png"; 
  }
  if($mother_pic!=""){
    $mother_photo = base_url(). $mother_pic ;
    
  }else{
    $mother_photo = base_url()."uploads/student_images/no_image.png";
  }
  if($guardian_pic!=""){
    $guardian_photo = base_url(). $guardian_pic ;
    
  }else{
    $guardian_photo = base_url()."uploads/student_images/no_image.png";
  }
 ?> 
 <div class="alert alert-success" id="completeformdiv" ><?php echo $this->lang->line('form_has_been_submitted_successfully'); ?> </div>
   <div id="divtoprint" class="spaceb50">
    <div class="row" id="printheader">
      <img src="<?php echo base_url() ?>/uploads/print_headerfooter/online_admission_receipt/<?php echo $this->setting_model->get_onlineadmissionheader(); ?>" style="height: 100px;width: 100%;" />
    </div>
  
      <div class="row">
        <div class="col-md-4 col-lg-4 col-sm-12">
           <h4 class="entered" id="headid"><?php echo $this->lang->line('review_entered_details_and_status'); ?></h4>
            <h4 class="entered" id="printheadid">Online Admission Form - Session 2025-26</h4>
        </div>
        <div class="col-md-8 col-lg-8 col-sm-12">
            <form action="<?php echo base_url().'welcome/editonlineadmission/'.$reference_no ; ?>" method="post" class="">
                <div class="row">   
                  <div class="col-md-10"></div>
                   <div class="col-md-2 col-lg-2 col-sm-12">  
                    <div class="statusright">
                      <?php if($form_status==0 && $status==""){ ?>
                       
                       <?php } ?>
                       <button type="button" id="printbtn" class="btn printbtndrakgray btn-sm" data-toggle="tooltip" title="<?php echo $this->lang->line('print'); ?>" onclick="printDiv('divtoprint')"><i class="fa fa-print"></i></button>
                    </div>   
                  </div>   
              </div>       
            </form>            
        </div>
    </div>
   
     <hr/>
     <!---<div id="divtoprint">-->

         <div class="row justify-content-center align-items-center flex-wrap d-flex">
            <div class="col-md-7 col-lg-7 col-sm-8">
             
              <ul class="reflist">
                <li><?php echo $this->lang->line('reference_no')  ?><span><?php echo $reference_no; ?></span></li>

                <?php if($form_status==0){ ?>
                <li><?php echo $this->lang->line('form_status') ?><span class="text-danger"><?php echo $this->lang->line('not_submitted') ?></span></li>
                <?php }else{ ?>
                     <li><?php echo $this->lang->line('form_status') ?><span class="text-success"><?php echo $this->lang->line('submitted') ?></span></li>
                  <?php } ?>
                  <?php 
                   if($online_admission_payment=='yes'){  

                            if($paid_status==1){ ?>
                               <li><?php echo $this->lang->line('payment_status'); ?><span class="text-success"><?php echo $this->lang->line('paid'); ?></span></li>
                               <li><?php echo $this->lang->line('paid_amount') ?><span><?php echo $currency_symbol .''. amountFormat($transaction_paid_amount) ; ?></span></li>
                               <li><?php echo $this->lang->line('transaction_id') ?><span><?php echo $transaction_id ; ?></span></li>                              
                            <?php }elseif($paid_status==2){ ?>
                               <li><?php echo $this->lang->line('payment_status'); ?><span class="text-success"><?php echo $this->lang->line('processing'); ?></span></li>
                               <li><?php echo $this->lang->line('transaction_id') ?><span><?php echo $transaction_id ; ?></span></li>
                            <?php }else{ ?>
                                <li><?php echo $this->lang->line('payment_status') ?><span class="text-danger"><?php echo $this->lang->line('unpaid') ?></span></li>
                              <?php }
                    }  ?>
                 <!-- Enrollment Status -->
                 <?php if($is_enroll == 1){ ?>
                    <li>Enrollment Status<span class="text-success"><?php echo $this->lang->line('enrolled'); ?></span></li>
                 <?php }else{ ?>
                    <li>Enrollment Status<span class="text-danger">Not Enrolled</span></li>
                 <?php } ?>
                 <li><?php echo $this->lang->line('application_date') ?><span><?php echo $application_date ; ?></span></li>
              </ul>
             
            </div>
            <div class="col-md-5 col-lg-5 col-sm-4">
               <ul class="statusimg">
                   <?php  if ($this->customlib->getfieldstatus('student_photo')) { ?>
                   <li> 
                       <img src="<?php echo $student_photo.img_time(); ?>" />

                       <p>Photo</p>
                   </li>
                 <?php } if ($this->customlib->getfieldstatus('father_pic')) { ?>  
                   <li>
                       <img src="<?php echo $father_photo.img_time(); ?>" />
                      <p><?php echo $this->lang->line('father'); ?></p>
                   </li>
                 
                 <?php } if($this->customlib->getfieldstatus('guardian_photo')){ ?>
                   <li>
                       <img src="<?php echo $guardian_photo.img_time(); ?>" />
                       <p>Signature</p>
                   </li>
                 <?php } ?>
               </ul>
            </div>
         </div><!--./row-->
        <br/>

        <?php if($online_admission_payment=='yes'){ ?>
        <br/>
          <form id="paymentform" action="<?php echo base_url(); ?>onlineadmission/checkout" method="post" >
        <?php } else { ?>
          <form id="admissionform" action="<?php echo base_url(); ?>welcome/submitadmission" method="post">
        <?php } ?>
        <input type="hidden" name="admission_id" value="<?php echo $admission_id ; ?>">
        <input type="hidden" name="reference_no" value="<?php echo $reference_no ; ?>">

        <?php if($online_admission_payment=='yes' && $paid_status==0 && $status==""){ ?>
          <div class="row no-print" style="margin-bottom: 30px;">
            <div class="col-md-12">
              <div class="payment-section" style="border: 2px solid #b30738; border-radius: 10px; padding: 20px; position: relative;">
                <h4 style="background: #b30738; color: white; padding: 8px 15px; position: absolute; top: -18px; left: 20px; margin: 0; border-radius: 5px; font-family: 'Rubik', sans-serif; font-size: 16px;">Online Admission Form Payment</h4>
                <div style="margin-top: 15px;" class="row btnprint relative">
                  <div class="col-md-4">
                    <div style="display: flex; flex-direction: column;">
                      <select class="form-control currency_list" name="currency" style="width: 100%; display: block;">
                        <?php 
                          foreach ($currencies as $currencie_key => $currencie_value) {  
                        ?>
                        <option value="<?php echo $currencie_value->id; ?>" <?php
                        if ( $currencie_value->id == $this->customlib->getSchoolCurrency()) {
                            echo "Selected";
                        }
                        ?> ><?php echo $currencie_value->short_name." (".$currencie_value->symbol.")"; ?></option>
                        <?php
                        }
                        ?>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-8">
                    <div style="display: flex; align-items: center; justify-content: flex-end; height: 100%;">
                      <div style="display: flex; align-items: center; margin-right: 15px; margin-left: 15px;">
                        <input type="checkbox" id="checkterm" name="checkterm" style="margin-right: 5px;">&nbsp; 
                        <a href="#openTermsAndConditionsModal" data-toggle="modal" data-target="#openTermsAndConditionsModal"><?php echo $this->lang->line('i_agree_to_the_terms_and_conditions'); ?></a>
                      </div>
                      <button type="submit" id="paybtn" class="btn btn-danger" style="margin-top: 0;"> <?php echo $this->lang->line('pay') ?>  <?php echo $currency_symbol. amountFormat($online_admission_amount) ?> </button>
                      <span class="text-danger" id="termerror" style="display: block; margin-left: 10px;"></span>
                    </div>
                  </div>
                  <div id="fade"></div>
                  <div id="modal">      
                    <i class="fa fa-spinner fa-spin fa-1x fa-fw"></i><span class="sr-only">Loading...</span>
                    <img id="loader" src="">                            
                  </div>
                </div>
              </div>
            </div>
          </div>
        <?php } else if($form_status==0 && $status=="") { ?>
          <div class="row no-print" style="margin-bottom: 30px;">
            <div class="col-md-12">
              <div class="payment-section" style="border: 2px solid #b30738; border-radius: 10px; padding: 20px; position: relative;">
                <h4 style="background: #b30738; color: white; padding: 8px 15px; position: absolute; top: -18px; left: 20px; margin: 0; border-radius: 5px; font-family: 'Rubik', sans-serif; font-size: 16px;">Online Admission Form Submission</h4>
                <div style="margin-top: 15px;" class="row btnprint">
                  <div class="col-md-12">
                    <div style="display: flex; align-items: center; justify-content: flex-end;">
                      <div style="display: flex; align-items: center; margin-right: 15px; margin-left: 15px;">
                        <input type="checkbox" id="checkterm" name="checkterm" style="margin-right: 5px;">&nbsp; 
                        <a href="#openTermsAndConditionsModal" data-toggle="modal" data-target="#openTermsAndConditionsModal"><?php echo $this->lang->line('i_agree_to_the_terms_and_conditions'); ?></a>
                      </div>
                      <button type="submit" class="btn btn-danger" id="submitbtn" style="margin-top: 0;"><?php echo $this->lang->line('submit'); ?></button>
                      <span class="text-danger" id="termerror" style="display: block; margin-left: 10px;"></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        <?php } ?>
       
        
        
          <div class="printcontent">
            <div class="row">
              <h4 class="pagetitleh3"><?php echo $this->lang->line('basic_details'); ?></h4>
              <div class="col-md-3 col-lg-3 col-sm-3">
                  <div class="form-group">
                    <label><b><?php echo $this->lang->line('class'); ?></b></label>
                    <p><?php  echo $class_name ; ?></p> 
                  </div>
              </div>     

              <div class="col-md-3 col-lg-3 col-sm-3">
                  <div class="form-group">
                      <label><b><?php echo $this->lang->line('first_name'); ?></b></label>
                      <p><?php echo $firstname ; ?></p>
                  </div>
              </div> 
             <?php if ($this->customlib->getfieldstatus('middlename')) {?>   
             <div class="col-md-3 col-lg-3 col-sm-3">
                <div class="form-group">
                  <label><b><?php echo $this->lang->line('middle_name'); ?></b></label>
                  <p><?php if( $middlename!=""){ echo  $middlename ; }else{ echo "--" ; }  ?></p>
                </div>
            </div> 
            <?php } ?>
             <?php if ($this->customlib->getfieldstatus('lastname')) {?>   
             <div class="col-md-3 col-lg-3 col-sm-3">
                <div class="form-group">
                  <label><b><?php echo $this->lang->line('last_name'); ?></b></label>
                  <p><?php if($lastname!=""){ echo $lastname; }else{ echo "--"; } ?></p>
                </div>
              </div> 
              <?php } ?>
              <div class="col-md-3 col-lg-3 col-sm-3">
                <div class="form-group">
                  <label><b><?php echo $this->lang->line('gender'); ?></b></label>
                  <p><?php echo $gender; ?></p>
                </div>
              </div> 
              <div class="col-md-3 col-lg-3 col-sm-3">
                <div class="form-group">
                  <label><b><?php echo $this->lang->line('date_of_birth'); ?></b></label>
                  <p><?php echo date($this->customlib->getSchoolDateFormat(), $this->customlib->dateyyyymmddTodateformat($dob)); ?></p>
                </div>
              </div>
        </div> 
      <div class="row">
        <?php if ($this->customlib->getfieldstatus('mobile_no')) {?>
        <div class="col-md-3 col-lg-3 col-sm-3">
          <div class="form-group">
              <label><b><?php echo $this->lang->line('mobile_number'); ?></b></label>
              <p><?php if($mobileno!=""){ echo $mobileno; }else{ echo "--"; } ?></p>
          </div>
        </div> 
        <?php } ?>
        <?php if ($this->customlib->getfieldstatus('student_email')) {?>
         <div class="col-md-3 col-lg-3 col-sm-3">
            <div class="form-group">
              <label><b><?php echo $this->lang->line('email'); ?></b></label>
              <p><?php echo $email; ?></p>
            </div>
        </div> 
         <?php } ?>
        <?php if ($this->customlib->getfieldstatus('category')) {?>
          <div class="col-md-3 col-lg-3 col-sm-3">
            <div class="form-group">
              <label><b><?php echo $this->lang->line('category'); ?></b></label>
              <p><?php if($category!=""){ echo $category; }else{ echo "--"; } ?></p>
            </div>
          </div>
        <?php } ?>
      </div>       

      <div class="row">
        <?php if ($this->customlib->getfieldstatus('religion')) {?>
        <div class="col-md-3 col-lg-3 col-sm-3">
            <div class="form-group">
              <label><b><?php echo $this->lang->line('religion'); ?></b></label>
              <p><?php if($religion!=""){ echo $religion; }else{ echo "--"; } ?></p>
            </div>
        </div> 
        <?php } ?>
        <?php if ($this->customlib->getfieldstatus('is_blood_group')) { ?>
        <div class="col-md-3 col-lg-3 col-sm-3">
            <div class="form-group">
              <label><b><?php echo $this->lang->line('blood_group'); ?></b></label>
              <p><?php if($blood_group!=""){ echo $blood_group; }else{ echo "--"; } ?></p>
            </div>
        </div>
        <?php } ?>
        <?php if ($this->customlib->getfieldstatus('national_identification_no')) { ?>
        <div class="col-md-3 col-lg-3 col-sm-3">
            <div class="form-group">
                <label><b><?php echo $this->lang->line('national_identification_number'); ?></b></label>
                <p><?php if($adhar_no!=""){ echo $adhar_no; }else{ echo "--"; } ?></p>
            </div>
        </div> 
        <?php } ?>
      </div>

      <div class="row">
        <?php if ($this->customlib->getfieldstatus('cast')) {?>
        <div class="col-md-3 col-lg-3 col-sm-3">
            <div class="form-group">
              <label><b><?php echo $this->lang->line('caste'); ?></b></label>
              <p><?php if($cast!=""){ echo $cast; }else{ echo "--"; } ?></p>
            </div>
        </div> 
        <?php } ?>
        <?php if ($this->customlib->getfieldstatus('is_student_house')) { ?>
        <div class="col-md-3 col-lg-3 col-sm-3">
            <div class="form-group">
              <label><b><?php echo $this->lang->line('house'); ?></b></label>
              <p><?php if($house_name!=""){ echo $house_name; }else{ echo "--"; } ?></p>
            </div>
        </div>
        <?php } ?>
        <?php if ($this->customlib->getfieldstatus('student_height')) { ?>
        <div class="col-md-3 col-lg-3 col-sm-3">
            <div class="form-group">
              <label><b><?php echo $this->lang->line('height'); ?></b></label>
              <p><?php if($height!=""){ echo $height; }else{ echo "--"; } ?></p>
            </div>
        </div>
        <?php } ?>
        <?php if ($this->customlib->getfieldstatus('student_weight')) { ?>
        <div class="col-md-3 col-lg-3 col-sm-3">
            <div class="form-group">
              <label><b><?php echo $this->lang->line('weight'); ?></b></label>
              <p><?php if($weight!=""){ echo $weight; }else{ echo "--"; } ?></p>
            </div>
        </div>
        <?php } ?>
      </div>

      <?php if ($this->customlib->getfieldstatus('measurement_date')) { ?>
      <div class="row">
        <div class="col-md-3 col-lg-3 col-sm-3">
            <div class="form-group">
              <label><b><?php echo $this->lang->line('measurement_date'); ?></b></label>
              <p><?php if($measurement_date!=""){ echo $measurement_date; }else{ echo "--"; } ?></p>
            </div>
        </div>
      </div>
      <?php } ?>

      <div class="row">
        <?php
        $cutom_fields_data = get_onlineadmission_custom_table_values($id, 'students');
        if (!empty($cutom_fields_data)) {
            $current_group = '';
            foreach ($cutom_fields_data as $field_key => $field_value) {
                if ($this->customlib->getfieldstatus($field_value->name)) {
                    // Skip fields without values
                    if (empty($field_value->field_value)) {
                        continue;
                    }
                    
                    // Handle group headers
                    if ($field_value->group_name !== $current_group) {
                        if ($current_group !== '') {
                            echo '</div><div class="row">'; // Close previous row and start new
                        }
                        if (!empty($field_value->group_name)) {
                            echo '<div class="col-md-12"><h4 class="pagetitleh2">' . ucfirst($field_value->group_name) . '</h4></div>';
                        }
                        $current_group = $field_value->group_name;
                    }
                    ?>
                    <div class="col-md-3 col-lg-3 col-sm-3">
                        <div class="form-group">
                            <label><b><?php echo $field_value->name; ?></b> </label>
                            <p>
                                <?php
                                if (is_string($field_value->field_value) && is_array(json_decode($field_value->field_value, true)) && (json_last_error() == JSON_ERROR_NONE)) {
                                    $field_array = json_decode($field_value->field_value);
                                    
                                    foreach ($field_array as $each_key => $each_value) {
                                        echo $each_value;
                                    }
                                    
                                } else {
                                    $display_field = $field_value->field_value ?: "--"; // Show "--" for empty fields
                                    
                                    if ($field_value->type == "link" && !empty($field_value->field_value)) {
                                        $display_field = "<a href=" . $field_value->field_value . " target='_blank'>" . $field_value->field_value . "</a>";
                                    }
                                    echo $display_field;
                                }
                                ?>
                            </p>
                        </div>
                    </div>
                    <?php
                }
            }
        }
        ?>
      </div>
</div>
 
      
        <div class="printcontent">
          <div class="row">
            <h4 class="pagetitleh3"><?php echo $this->lang->line('parent_detail'); ?></h4>
               <?php if( $this->customlib->getfieldstatus('father_name') || $this->customlib->getfieldstatus('father_phone') || $this->customlib->getfieldstatus('father_occupation') || $this->customlib->getfieldstatus('father_pic') || $this->customlib->getfieldstatus('mother_name') || $this->customlib->getfieldstatus('mother_phone') || $this->customlib->getfieldstatus('mother_occupation') || $this->customlib->getfieldstatus('mother_pic') ){ ?>
            <?php if ($this->customlib->getfieldstatus('father_name')) {?> 
             <div class="col-md-3 col-lg-3 col-sm-3">
                <div class="form-group">
                    <label><b><?php echo $this->lang->line('father_name'); ?></b> </label>
                    <p><?php if($father_name!=""){ echo  $father_name ; }else{ echo "--" ; } ?></p>
                </div>
            </div> 
            <?php } ?>
            <?php if ($this->customlib->getfieldstatus('father_phone')) {?>
             <div class="col-md-3 col-lg-3 col-sm-3">
                <div class="form-group">
                 <label><b><?php echo $this->lang->line('father_phone'); ?></b> </label>
                  <p><?php if($father_phone!=""){ echo  $father_phone ; }else{ echo "--" ; } ?></p>
                </div>
            </div> 
            <?php } ?>
              <?php if ($this->customlib->getfieldstatus('father_occupation')) {?> 
            <div class="col-md-3 col-lg-3 col-sm-3">
                <div class="form-group">
                  <label><b><?php echo $this->lang->line('father_occupation'); ?></b> </label>
                  <p><?php if($father_occupation!=""){ echo $father_occupation ; }else{ echo "--" ; } ?></p>
                </div>
            </div> 
            <?php } ?>
        </div>    
         
        <div class="row">
             <?php if ($this->customlib->getfieldstatus('mother_name')) {?>  
             <div class="col-md-3 col-lg-3 col-sm-3">
                <div class="form-group">
                  <label><b><?php echo $this->lang->line('mother_name'); ?></b> </label>
                  <p><?php if($mother_name!=""){ echo  $mother_name ; }else{ echo "--" ; } ?></p>
                </div>
            </div> 
            <?php } ?>
             <?php if ($this->customlib->getfieldstatus('mother_phone')) {?>
             <div class="col-md-3 col-lg-3 col-sm-3">
                <div class="form-group">
                    <label><b><?php echo $this->lang->line('mother_phone'); ?></b> </label>
                    <p><?php if($mother_phone!=""){ echo  $mother_phone ; }else{ echo "--" ; } ?></p>
                </div>
            </div> 
            <?php } ?>
            <?php if ($this->customlib->getfieldstatus('guardian_email')) {?> 
               <div class="col-md-3 col-lg-3 col-sm-3">
                  <div class="form-group">
                      <label><b><?php echo $this->lang->line('guardian_email'); ?></b> </label>
                      <p><?php if($guardian_email!=""){ echo $guardian_email ; }else{ echo "--"  ; } ?></p>
                  </div>
              </div>
              <?php } ?>
             <?php if ($this->customlib->getfieldstatus('mother_occupation')) {?>
             <div class="col-md-3 col-lg-3 col-sm-3">
                <div class="form-group">
                 <label><b><?php echo $this->lang->line('mother_occupation'); ?></b> </label>
                  <p><?php if($mother_occupation!=""){ echo $mother_occupation; }else{ echo "--" ; }  ?></p>
                </div>
            </div> 
             <?php } ?>
        </div>     
      </div><!--./printcontent-->
    <?php } ?>
     <?php if ($this->customlib->getfieldstatus('if_guardian_is')) {?>
      
        <div class="printcontent">
          <div class="row">
            <h4 class="pagetitleh3"><?php echo $this->lang->line('guardian_details'); ?></h4>
             <div class="col-md-3 col-lg-3 col-sm-3">
                  <div class="form-group">
                      <label><b><?php echo $this->lang->line('if_guardian_is'); ?></b> </label>
                      <p><?php echo $this->lang->line($guardian_is); ?></p>
                  </div>
              </div> 

              <div class="col-md-3 col-lg-3 col-sm-3">
                  <div class="form-group">
                   <label><b><?php echo $this->lang->line('guardian_name'); ?></b> </label>
                    <p><?php  if($guardian_name!=""){ echo $guardian_name; }else{ echo "--" ; }  ?></p>
                  </div>
              </div> 
              <div class="col-md-3 col-lg-3 col-sm-3">
                  <div class="form-group">
                      <label><b><?php echo $this->lang->line('guardian_relation'); ?></b> </label>
                      <p><?php if($guardian_relation!=""){ echo $guardian_relation; }else{ echo "--" ; }  ?></p>
                  </div>
              </div>
          </div>   
         
          <div class="row">
            <?php if ($this->customlib->getfieldstatus('guardian_phone')) {?>
            <div class="col-md-3 col-lg-3 col-sm-3">
                <div class="form-group">
                    <label><b><?php echo $this->lang->line('guardian_phone'); ?></b> </label>
                    <p><?php  if($guardian_phone!=""){ echo $guardian_phone; }else{ echo "--" ; }  ?></p>
                </div>
            </div> 
             <?php } ?>
             <?php if ($this->customlib->getfieldstatus('guardian_occupation')) { ?>
             <div class="col-md-3 col-lg-3 col-sm-3">
                <div class="form-group">
                    <label><b><?php echo $this->lang->line('guardian_occupation'); ?></b> </label>
                    <p><?php if($guardian_occupation!=""){ echo $guardian_occupation ; }else{ echo "--" ; } ?></p>
                </div>
            </div>
            <?php } ?>          
             
            <?php if ($this->customlib->getfieldstatus('guardian_address')) {?>
             <div class="col-md-3 col-lg-3 col-sm-3">
                <div class="form-group">
                    <label><b><?php echo $this->lang->line('guardian_address'); ?></b> </label>
                    <p><?php if($guardian_address!=""){ echo $guardian_address ; }else{ echo "--" ; }  ?></p>
                </div>
            </div> 
            <?php } ?>
          </div>    
        </div><!--./printcontent-->
        <?php } ?>

        <?php if($this->customlib->getfieldstatus('current_address')  || $this->customlib->getfieldstatus('permanent_address') ){ ?>
      
        <div class="printcontent">
          <div class="row">
            <h4 class="pagetitleh3"><?php echo $this->lang->line('student_address_details'); ?></h4>
              <?php if ($this->customlib->getfieldstatus('current_address')) { ?>
                  <div class="col-md-6 col-sm-6">
                      <div class="form-group">
                          <label><b><?php echo $this->lang->line('current_address'); ?></b></label>
                         <p><?php if($current_address!=""){ echo  $current_address ; }else{ echo "--" ; } ; ?></p>
                      </div>
                  </div>
              <?php } ?>
                <?php if ($this->customlib->getfieldstatus('permanent_address')) { ?>
                  <div class="col-md-6 col-sm-6">
                      <div class="form-group">
                        <label><b><?php echo $this->lang->line('permanent_address'); ?></b></label>
                        <p><?php if($permanent_address!=""){ echo  $permanent_address ; }else{ echo "--" ; } ?></p>
                      </div>
                  </div>
              <?php } ?>
          </div>    
        </div>
        <?php } ?>
        <?php if( $this->customlib->getfieldstatus('bank_account_no') || $this->customlib->getfieldstatus('bank_name') || $this->customlib->getfieldstatus('ifsc_code') || $this->customlib->getfieldstatus('national_identification_no') || $this->customlib->getfieldstatus('local_identification_no') || $this->customlib->getfieldstatus('rte') || $this->customlib->getfieldstatus('previous_school_details') || $this->customlib->getfieldstatus('student_note') ) { ?>

    
     

      
      <!--  <?php } ?>            -->
                
                   </form>
                   </div>
                <div class="printcontent" id="printfooter">
                    <?php $this->setting_model->get_onlineadmissionfooter(); ?>   
                </div> 
            </div><!--./divtoprint-->
        </div><!--./spaceb50-->

<div class="footer-broodle">
    <p>© 2025 St. Xavier's College Jaipur. All Rights Reserved.</p>
    <p>Developed & Hosted by <a style="color: #ffffff;" href="https://broodle.host">Broodle</a></p>
</div>

<script type="text/javascript">
$(document).ready(function () {
  $("#printheadid").css('display','none');
  $("#printheader").css('display','none');
  $("#printfooter").css('display','none');
  $("#completeformdiv").css('display','none');

      if (sessionStorage.getItem("formsubmit") === null) {
        $("#completeformdiv").css('display','none');
      }else{
          $("#completeformdiv").css('display','block');
           sessionStorage.removeItem('formsubmit');
      }

});
</script>

<script type="text/javascript">

    function refreshCaptcha(){
        $.ajax({
            type: "POST",
            url: "<?php echo base_url('site/refreshCaptcha'); ?>",
            data: {},
            success: function(captcha){
                $("#captcha_image").html(captcha);
            }
        });
    }   

</script>

<script>
        function printDiv(){
          $("#printbtn").css('display','none');
          $("#editbtn").css('display','none');
          $(".btnprint").css('display','none');
          $("#headid").css('display','none');
          $("#printheadid").css('display','block');
          $("#printheader").css('display','block');
          $("#printfooter").css('display','block');
          $('.statusright').css('display','none');
          $('.footer-broodle').css('display','none'); // Hide footer when printing
		  
            // Fix for header stretching - add a container with controlled dimensions
            if ($("#printheader img").length) {
                $("#printheader").css({
                    'width': '100%',
                    'text-align': 'center'
                });
                
                $("#printheader img").css({
                    'max-width': '100%',
                    'height': 'auto',
                    'max-height': '100px',
                    'object-fit': 'contain'
                });
            }
            
            // Remove the receipt text if it exists
            $("#printfooter").remove();
            $(".receipt-text, .receipt-footer, .receipt-signature").remove();
            $('[id*="receipt"], [class*="receipt"], [id*="signature"], [class*="signature"]').remove();
            
            var printContents = document.getElementById('divtoprint').innerHTML;
            var originalContents = document.body.innerHTML;
            
            // Create a new window for better print control
            var printWindow = window.open('', '_blank');
            printWindow.document.write('<html><head><title>Admission Form</title>');
            
            // Add landscape orientation style and print-specific font sizes
            printWindow.document.write(`
                <style>
                    @page { 
                        size: landscape;
                        margin: 0.5cm;
                    }
                    @media print {
                        body {
                            font-size: 11px !important;
                            padding: 0 !important;
                            margin: 0 !important;
                            line-height: 1.2 !important;
                        }

                        #divtoprint {
                            padding: 0 !important;
                            margin: 0 !important;
                            background-color: white !important;
                        }

                        /* Compact spacing for 2-page layout */
                        .printcontent {
                            margin-bottom: 8px !important;
                            page-break-inside: auto !important;
                            padding: 0 !important;
                        }

                        /* Minimal spacing between sections */
                        .printcontent {
                            position: relative !important;
                            margin-top: 5px !important;
                            padding-top: 0 !important;
                        }
                        
                        /* Balanced layout with selective page breaks */
                        /* Parent details section - let it flow naturally */
                        .printcontent:nth-of-type(3) {
                            page-break-before: auto !important;
                        }
                        
                        /* Guardian details - start on new page if needed */
                        .printcontent:nth-of-type(4) {
                            page-break-before: auto !important;
                        }
                        
                        /* Compact section headers */
                        h4.pagetitleh3 {
                            page-break-after: avoid !important;
                            margin-top: 8px !important;
                            margin-bottom: 3px !important;
                            font-size: 13px !important;
                            padding: 3px 0 !important;
                        }

                        h4.pagetitleh2 {
                            page-break-after: avoid !important;
                            margin-top: 8px !important;
                            margin-bottom: 3px !important;
                            font-size: 13px !important;
                            padding: 4px 5px !important;
                        }

                        /* Compact row spacing */
                        .row {
                            page-break-inside: auto !important;
                            margin-bottom: 2px !important;
                        }
                        
                        /* Compact column layout */
                        .col-md-3, .col-lg-3, .col-sm-3 {
                            flex: 0 0 25% !important;
                            max-width: 25% !important;
                            padding: 0 2px !important;
                        }

                        /* Balanced form elements */
                        .form-group {
                            margin-bottom: 2px !important;
                            padding: 3px 4px 1px 4px !important;
                            min-height: 35px !important;
                        }
                        
                        /* Compact section layout */
                        .printcontent .row {
                            display: flex !important;
                            flex-wrap: wrap !important;
                            margin: 0 !important;
                            padding: 1px 5px !important;
                        }

                        .printcontent .row:first-of-type {
                            margin-bottom: 2px !important;
                        }

                        /* Compact columns */
                        .col-md-3, .col-lg-3, .col-sm-3 {
                            flex: 0 0 25% !important;
                            max-width: 25% !important;
                            padding: 0 2px !important;
                        }

                        /* Minimal form group spacing */
                        .form-group {
                            margin-bottom: 1px !important;
                            padding: 1px 2px !important;
                        }

                        /* No extra spacing between rows */
                        .row + .row {
                            margin-top: 0 !important;
                        }

                        /* Compact section titles */
                        .pagetitleh3 {
                            font-size: 13px !important;
                            font-weight: bold !important;
                            padding: 2px 0 !important;
                            margin: 5px 0 2px !important;
                            width: 100% !important;
                        }

                        .pagetitleh2 {
                            font-size: 13px !important;
                            font-weight: bold !important;
                            padding: 4px 5px !important;
                            margin: 8px 0 4px 0 !important;
                            border-bottom: 1px solid #ddd !important;
                            width: 100% !important;
                            background: #f8f8f8 !important;
                            color: #333 !important;
                            display: block !important;
                            page-break-after: avoid !important;
                            text-align: left !important;
                            border-radius: 2px 2px 0 0 !important;
                            position: relative !important;
                            box-sizing: border-box !important;
                            clear: both !important;
                        }

                        /* Compact custom field headings */
                        .col-md-12 h4.pagetitleh2 {
                            width: 100% !important;
                            margin-top: 8px !important;
                            margin-bottom: 4px !important;
                            padding: 4px 5px !important;
                            box-sizing: border-box !important;
                            clear: both !important;
                            display: block !important;
                        }

                        /* Minimal spacing for custom fields section */
                        .col-md-12:has(h4.pagetitleh2) {
                            margin-top: 4px !important;
                            float: none !important;
                            width: 100% !important;
                            clear: both !important;
                        }

                        h4.entered {
                            font-size: 13px !important;
                            font-weight: bold !important;
                            padding: 3px 0 !important;
                            margin: 5px 0 3px !important;
                            border-bottom: 1px solid #333 !important;
                            width: 100% !important;
                            text-align: center !important;
                        }

                        /* Compact grid layout */
                        .row {
                            display: flex !important;
                            flex-wrap: wrap !important;
                            margin: 0 !important;
                            padding: 2px 5px 4px 5px !important;
                        }

                        .col-md-3, .col-lg-3, .col-sm-3,
                        .col-md-4, .col-lg-4, .col-sm-4,
                        .col-md-6, .col-lg-6, .col-sm-6 {
                            flex: 0 0 33.333333% !important;
                            max-width: 33.333333% !important;
                            padding: 0 2px !important;
                            margin-bottom: 2px !important;
                        }

                        /* Balanced form group styles */
                        .form-group {
                            background: white !important;
                            border: 1px solid #ddd !important;
                            border-radius: 2px !important;
                            padding: 3px 4px 1px 4px !important;
                            height: auto !important;
                            min-height: 35px !important;
                        }

                        .form-group label {
                            display: block !important;
                            font-weight: bold !important;
                            margin-bottom: 2px !important;
                            color: #666 !important;
                            font-size: 8px !important;
                            text-transform: uppercase !important;
                            line-height: 1.1 !important;
                        }

                        .form-group p {
                            margin: 0 !important;
                            font-size: 9px !important;
                            color: #333 !important;
                            line-height: 1.1 !important;
                            padding-top: 0px !important;
                            padding-bottom: 0px !important;
                        }

                        /* Compact section styles */
                        .printcontent {
                            margin-bottom: 4px !important;
                        }

                        /* Reorganized top section layout */
                        .row.justify-content-center {
                            display: flex !important;
                            flex-direction: row !important;
                            align-items: flex-start !important;
                        }

                        /* Reference section - move to left, make narrower */
                        .col-md-7:has(.reflist) {
                            flex: 0 0 60% !important;
                            max-width: 60% !important;
                            padding-right: 5px !important;
                        }

                        /* Image section - move to right, make narrower */
                        .col-md-5:has(.statusimg) {
                            flex: 0 0 40% !important;
                            max-width: 40% !important;
                            padding-left: 5px !important;
                        }

                        /* Compact image section */
                        .statusimg {
                            display: flex !important;
                            justify-content: flex-end !important;
                            gap: 3px !important;
                            margin-bottom: 5px !important;
                        }

                        .statusimg li {
                            text-align: center !important;
                            border: 1px solid #ddd !important;
                            padding: 1px !important;
                            border-radius: 2px !important;
                        }

                        .statusimg img {
                            width: 100px !important;
                            height: 100px !important;
                            object-fit: cover !important;
                            border-radius: 2px !important;
                        }

                        .statusimg p {
                            margin: 1px 0 0 !important;
                            font-size: 7px !important;
                        }
                        
                       
                        
                        .reflist  {
                            min-width: auto !important;
                            width: 100% !important;
                            margin-bottom: -10px !important;
                            margin-top: -5px !important;
                        }

                        /* Additional space optimizations for 2-page layout */
                        * {
                            box-sizing: border-box !important;
                        }

                        /* Reduce overall page margins */
                        @page {
                            margin: 0.3in !important;
                            size: A4 !important;
                        }

                        /* Ensure sections don't break awkwardly */
                        .printcontent h4 {
                            page-break-after: avoid !important;
                        }

                        /* Optimize table-like layouts */
                        .row .col-md-3 {
                            page-break-inside: avoid !important;
                        }

                        /* Properly spaced status list */
                        .reflist li {
                            margin-bottom: 2px !important;
                            padding: 3px 5px !important;
                            font-size: 9px !important;
                            line-height: 1.2 !important;
                            border-bottom: 1px solid #eee !important;
                        }

                        /* Center align the print header title */
                        #printheadid {
                            text-align: center !important;
                            width: 100% !important;
                            margin: 10px 0 !important;
                            font-size: 14px !important;
                            font-weight: bold !important;
                            color: #333 !important;
                        }

                        /* Make the title container span full width in print */
                        .col-md-4:has(#printheadid) {
                            flex: 0 0 100% !important;
                            max-width: 100% !important;
                            text-align: center !important;
                        }
                    }
                </style>
            `);
            
            // Copy all stylesheets from the current document
            var stylesheets = document.getElementsByTagName('link');
            for (var i = 0; i < stylesheets.length; i++) {
              if (stylesheets[i].rel === 'stylesheet') {
                printWindow.document.write(stylesheets[i].outerHTML);
              }
            }
            
            // Copy all styles from the current document
            var styles = document.getElementsByTagName('style');
            for (var i = 0; i < styles.length; i++) {
              printWindow.document.write(styles[i].outerHTML);
            }
            
            
            printWindow.document.write('</head><body>');
            printWindow.document.write(printContents);
            printWindow.document.write('</body></html>');
            printWindow.document.close();
            
            // Wait for resources to load before printing
            printWindow.onload = function() {
                // Apply additional styling to the header in the print window
                if (printWindow.document.getElementById('printheader')) {
                    var headerImg = printWindow.document.querySelector('#printheader img');
                    if (headerImg) {
                        headerImg.style.maxWidth = '100%';
                        headerImg.style.width = 'auto';
                        headerImg.style.height = 'auto';
                        headerImg.style.maxHeight = '100px';
                        headerImg.style.objectFit = 'contain';
                    }
                }
                
                // Remove the receipt text in the print window
                var footerElements = printWindow.document.querySelectorAll('#printfooter, .receipt-text, .receipt-footer, .receipt-signature');
                footerElements.forEach(function(element) {
                  if (element) element.remove();
                });
                
                // Use a more general selector to find and remove the text
                var receiptElements = printWindow.document.querySelectorAll('[id*="receipt"], [class*="receipt"], [id*="signature"], [class*="signature"]');
                receiptElements.forEach(function(element) {
                  if (element) element.remove();
                });
                
                printWindow.focus();
                printWindow.print();
                printWindow.close();
            };
            
            // Restore original content
            $("#headid").css('display','block');
            $("#printbtn").removeAttr('style');
            $("#editbtn").removeAttr('style');
            $('.footer-broodle').removeAttr('style');
        }
</script>

<script type="text/javascript">

$(document).ready(function(){ 
$(document).on('submit','#admissionform',function(e){
   e.preventDefault(); // avoid to execute the actual submit of the form.
    var form = $(this);
    $("#submitbtn").prop('disabled',true);
    var url = form.attr('action');
    var form_data = form.serializeArray();
    sessionStorage.removeItem('formsubmit');
   
    $.ajax({
           url: url,
           type: "POST",
           dataType:'JSON',
           data: form_data, // serializes the form's elements.
              beforeSend: function () {
               },
              success: function(response) { // your success handler

                if(response.status==0){
                    $('#termerror').html(response.error);
                } else{
                  
                  var admission_id= response.id;
                  var reference_no= response.reference_no;
                  sessionStorage.setItem("formsubmit", "done");
                  window.location.href="<?php echo base_url().'welcome/online_admission_review/' ?>"+reference_no ;
                }
              },
             error: function() { // your error handler
             
             },
             complete: function() {
           
             }  
         });
    });
});
</script>

<script>

  $(document).ready(function() {  
    if($('#checkterm').prop("checked")==true){
       $("#paybtn").prop('disabled',false);
       $("#submitbtn").prop('disabled',false);       
    }else{
      $("#paybtn").prop('disabled',true);
      $("#submitbtn").prop('disabled',true);
    }

    $('#checkterm').change(function() {
        if(this.checked) {
          $("#paybtn").prop('disabled',false);
          $("#submitbtn").prop('disabled',false);
        }else{
          $("#paybtn").prop('disabled',true);
           $("#submitbtn").prop('disabled',true);
        }       
    });
});
</script>

<script>
$(document).ready(function(){
  $('[data-toggle="tooltip"]').tooltip();
});

$(document).on('change','.currency_list',function(e){
 
   let currency_id=$(this).val();
   $.ajax({
      type: 'POST',
      url: base_url+'welcome/changeCurrencyFormat',
      data: {'currency_id':currency_id},
      dataType: 'json',
      beforeSend: function() {
      $('#fade').css("display", "block");
      $('#modal').css("display", "block");
      },
      success: function(data) {       
         $("#fade").fadeOut(2000); 
         $("#modal").fadeOut(2000); 
         window.location.reload();
      },
      error: function(xhr) { // if error occured
    
      },
      complete: function() {
        
      }     
  });
});
</script>