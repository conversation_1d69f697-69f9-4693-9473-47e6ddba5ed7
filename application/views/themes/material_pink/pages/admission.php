<script src="<?php echo base_url('backend/dist/js/moment.min.js'); ?>"></script>
<link rel="stylesheet" href="<?php echo base_url(); ?>backend/datepicker/css/bootstrap-datetimepicker.css">
<script src="<?php echo base_url(); ?>backend/datepicker/js/bootstrap-datetimepicker.js"></script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300..900;1,300..900&display=swap" rel="stylesheet">

<!-- Add these styles to the top of the file -->
<style>


    @media screen and (max-width: 700px) {
    .container {
        padding-right: 0px;
        padding-left: 0px;
    }
     .nav-links {
         display: none !important;
     }


     .admission-container {

    padding: 15px !important;

     }
}

  .broodle-header-text {
    font-family: 'Times New Roman', sans-serif !important;
    font-weight: 700 !important;
    letter-spacing: -1.5px !important;
    margin: 0 !important;
    font-size: 35px !important; /* Desktop size */
}

/* Media query for smaller screens (adjust max-width as needed) */
@media (max-width: 768px) { /* Common breakpoint for mobile */
    .broodle-header-text {
        font-size: 20px !important; /* Mobile size */
    }
}

    .title {
        font-size: 16px;
        font-weight: bold;
        margin: 10px 0;
        font-family: "Rubik", sans-serif;
    }

    h2, h3, h4, h5, h6 {
         font-family: "Rubik", sans-serif !important;
         font-size: 16px !important;
         font-weight: bold !important;
    }
    .info {
        margin-bottom: 20px;
    }
    .table-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
    .left-column { text-align: left; }
    .right-column { text-align: right; }
    .completion-status {
        text-align: center;
        font-weight: bold;
        margin: 20px 0;
    }
    body {
        background-color: #b30738;
        min-height: 100vh;
        position: relative;
        padding-bottom: 80px; /* Height of footer plus some spacing */
        margin: 0;
        font-family: "Rubik", sans-serif;
    }

    .admission-container {
        background-color: #fcfcfc;
        border-radius: 15px;
        padding: 30px;
        margin: 60px auto;
        max-width: 1200px;
    }

    .footer-broodle {
        background-color: black;
        color: white;
        text-align: center;
        padding: 15px 0;
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        font-size: 14px;
        line-height: 1.5;
    }

    .footer-broodle p {
        margin: 0;
    }

    .row {
    margin-right: 0;
    margin-left: 0;
}
.pagetitleh2 {
    background: #0000;
    border-bottom: 0px solid #ffffff;
    font-family: "Rubik", sans-serif;
}

.printcontent {

    padding: 5px 5px 5px 5px;
}
.onlineform .form-control {

    border-radius: 6px;
}
 header {
            background-color: #f8f0f0; /* Light pink background */
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            font-family: "Rubik", sans-serif;
            border-radius: 15px;
            top: 20px;
        }

        .logo {
            display: flex;
            align-items: center;
        }

        .logo img {
            height: 60px; /* Adjust as needed */
            margin-right: 10px;
        }

        .nav-links {
            display: flex;
            align-items: center;
                font-family: "Rubik", sans-serif;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            margin-left: 20px;
                font-family: "Rubik", sans-serif;
        }

        .nav-links .login-btn {
            background-color: #A31621; /* Dark red button */
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
                font-family: "Rubik", sans-serif;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            header {
                flex-direction: column;
                align-items: center;
            }


            .nav-links {
                flex-direction: column;
                align-items: flex-start;
            }

            .nav-links a {
                margin: 5px 0;
            }
        }

        .broodle-small {
            font-weight: 400;
            font-size: 13px;
        }
</style>

<header>
       <a style="color: black;" href="https://sxcjpr.edu.in" target="_blank"> <div class="logo">
            <img src="https://sxcjpr.edu.in/wp-content/uploads/2023/07/Xaviers-Jaipur-Logo.png" alt="St. Xavier's College Jaipur Logo">

            <h1 class="broodle-header-text">St. Xavier's College Jaipur</span>

        </div></a>
        <nav class="nav-links">
            <a href="https://sxcjpr.edu.in" target="_blank">Home</a>

            <a href="https://sxcjpr.edu.in/contact" target="_blank">Contact Us</a>
            <!--<a href="https://erp.sxcjpr.edu.in/site/userlogin" target="_blank" class="login-btn">ERP Login</a>-->
            &nbsp; &nbsp; &nbsp;
             <img width="120px" src="https://sxcjpr.edu.in/wp-content/uploads//2023/06/NAAC-Accredation-1.svg" alt="St. Xavier's College Jaipur Logo">
        </nav>
    </header>

<!-- After the header and before the form content, wrap everything in admission-container -->
<div class="admission-container">
    <?php
    if (!$form_admission) {
        ?>
        <div class="alert alert-danger">
            <?php echo $this->lang->line('admission_form_disable_please_contact_to_administrator'); ?>
        </div>
        <?php
    return;
    }
    ?>
    <?php

    if ($this->session->flashdata('msg')) {
        $message = $this->session->flashdata('msg');
        echo $message;
        $this->session->unset_userdata('msg');
    }
    ?>

    <div class="row justify-content-center align-items-center flex-wrap d-flex pt20">
        <!--<div class="col-md-6 col-lg-5 col-sm-5">-->
        <!--    <h3 class="entered mt0"><?php echo $this->lang->line('online_admission'); ?></h3>-->
        <!--    <h1>Broodle</h1>-->
        <!--</div>-->
        <div class="text-lg"><center>
        <div class="title">ONLINE ADMISSION FORM 2025-26</div>
            <a href="#checkOnlineAdmissionStatus" class="modalclosebtn modal-close-xs w-full-xs mr-lg-1" onclick="openStatusFormmodal();" data-toggle="modal" data-target="#checkOnlineAdmissionStatus"><?php echo $this->lang->line('check_your_form_status') ?></a>
            <?php if (!empty($online_admission_application_form)) {?>
            <a href="<?php echo base_url(); ?>welcome/download/<?php echo $sch_setting->id; ?>" class='modalclosebtn modal-close-xs w-full-xs text-center'><?php echo $this->lang->line('download_application_form'); ?></a>
            <?php }?>
        </div></center>
    </div>
   <form id="form1" class="spaceb60 spacet40 onlineform" action="<?php echo current_url() ?>"   method="post" accept-charset="utf-8" enctype="multipart/form-data">
        <?php if ($online_admission_instruction != "") {?>
        <div class="printcontent">
        <div class="row">
         <h4 class="pagetitleh2"><?php echo $this->lang->line('instructions'); ?></h4>
            <div class="col-md-12">
              <div class="form-group">
                    <?php echo $online_admission_instruction; ?>
                </div>
            </div>
        </div>
    </div>
  <?php }?>
 <div class="printcontent">
    <div class="row">
    <h4 class="pagetitleh2"><?php echo $this->lang->line('basic_details'); ?>
    <span class="broodle-small">&nbsp;(As per 10<sup>th</sup> class marksheet | Fields marked <font color="red">*</font> are mandatory to be filled)</span></h4>



        <div class="col-md-3 displaynone">
            <div class="form-group">
                <label><?php echo $this->lang->line('section'); ?></label><small class="req"> *</small>
                <select  id="section_id" name="section_id" class="form-control" >
                    <option value=""><?php echo $this->lang->line('select'); ?></option>
                </select>
                <span class="text-danger"><?php echo form_error('section_id'); ?></span>
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label><?php echo $this->lang->line('first_name'); ?></label><small class="req"> *</small>
                <input id="firstname" name="firstname" placeholder="" type="text" class="form-control"
                    value="<?php echo set_value('firstname'); ?>" autocomplete="on"
                    pattern="[A-Za-z\s]+" title="Only alphabets are allowed"
                    oninput="this.value = this.value.replace(/[0-9]/g, '')" />
                <span class="text-danger"><?php echo form_error('firstname'); ?></span>
            </div>
        </div>
         <?php if ($this->customlib->getfieldstatus('middlename')) {?>
         <div class="col-md-3">
            <div class="form-group">
                <label><?php echo $this->lang->line('middle_name'); ?></label>
                <input id="middlename" name="middlename" placeholder="" type="text" class="form-control"
                    value="<?php echo set_value('middlename'); ?>" autocomplete="on"
                    pattern="[A-Za-z\s]+" title="Only alphabets are allowed"
                    oninput="this.value = this.value.replace(/[0-9]/g, '')" />
            </div>
        </div>
        <?php }?>
        <?php if ($this->customlib->getfieldstatus('lastname')) {?>
         <div class="col-md-3">
            <div class="form-group">
                <label><?php echo $this->lang->line('last_name'); ?></label><small class="req"> *</small>
                <input id="lastname" name="lastname" placeholder="" type="text" class="form-control"
                    value="<?php echo set_value('lastname'); ?>" autocomplete="on" required
                    pattern="[A-Za-z\s]+" title="Only alphabets are allowed"
                    oninput="this.value = this.value.replace(/[0-9]/g, '')" />
                <span class="text-danger"><?php echo form_error('lastname'); ?></span>
            </div>
        </div>
        <?php }?>

        <div class="col-md-3">
            <div class="form-group">
                <label><?php echo $this->lang->line('gender'); ?></label><small class="req"> *</small>
                <select class="form-control" name="gender">
                    <option value=""><?php echo $this->lang->line('select'); ?></option>
                    <?php foreach ($genderList as $key => $value) { ?>
                        <option value="<?php echo $key; ?>" <?php if (set_value('gender') == $key) { echo "selected"; } ?>><?php echo $value; ?></option>
                        <?php } ?>
                </select>
                <span class="text-danger"><?php echo form_error('gender'); ?></span>
            </div>
        </div>
    </div><!--./row-->
    <div class="row">

        <?php if ($this->customlib->getfieldstatus('mobile_no')) {?>
        <div class="col-md-3">
            <div class="form-group">
                <label>Student's Mobile No.</label><small class="req"> *</small>
                <input type="tel" class="form-control" value="<?php echo set_value('mobileno'); ?>" id="mobileno" name="mobileno" autocomplete="on" pattern="[0-9]{10}" maxlength="10" title="Phone number must be 10 digits"/>
                <span class="text-danger"><?php echo form_error('mobileno'); ?></span>
            </div>
        </div>
        <?php }?>
         <?php if ($this->customlib->getfieldstatus('student_email')) {?>

        <div class="col-md-3">
            <div class="form-group">
                <label>Student's Email</label><small class="req"> *</small>
                <input type="text" class="form-control"  value="<?php echo set_value('email'); ?>" id="email" name="email" autocomplete="on"/>
                <span class="text-danger"><?php echo form_error('email'); ?></span>
            </div>
        </div>
        <?php }?>

        <div class="col-md-3">
            <div class="form-group">
                <label>Date of Birth</label><small class="req"> *</small>
                <input  type="text" class="form-control date2"  value="<?php echo set_value('dob'); ?>" id="dob" name="dob" readonly="readonly"/>
                <span class="text-danger"><?php echo form_error('dob'); ?></span>
            </div>
        </div>

         <?php if ($this->customlib->getfieldstatus('is_blood_group')) { ?>
        <div class="col-md-3 col-xs-12">
            <div class="form-group">
                <label><?php echo $this->lang->line('blood_group'); ?></label><small class="req"> *</small>
                    <?php ?>
                <select class="form-control" rows="3" placeholder="" name="blood_group" autocomplete="on">
                    <option value=""><?php echo $this->lang->line('select') ?></option>
                    <?php foreach ($bloodgroup as $bgkey => $bgvalue) { ?>
                        <option value="<?php echo $bgvalue ?>" <?php if (set_value('blood_group') == $bgvalue) { echo "selected"; } ?>><?php echo $bgvalue ?></option>
                    <?php } ?>
                </select>
                <span class="text-danger"><?php echo form_error('blood_group'); ?></span>
            </div>
        </div>
        <?php } ?>

    </div><!--./row-->
    <div class="row">
      <?php if ($this->customlib->getfieldstatus('category')) {
    ?>
        <div class="col-md-3">
            <div class="form-group">
               <label><?php echo $this->lang->line('category'); ?></label><small class="req"> *</small>
                    <select  id="category_id" name="category_id" class="form-control" autocomplete="on">
                        <option value=""><?php echo $this->lang->line('select'); ?></option>
                            <?php foreach ($categorylist as $category) {
                                // Skip category with ID 7
                                if ($category['id'] == 7) {
                                    continue;
                                }
                            ?>
                         <option value="<?php echo $category['id'] ?>" <?php
if (set_value('category_id') == $category['id']) {
        echo "selected=selected";
    }
        ?>><?php echo $category['category'] ?>
                         </option>
                                <?php
}
    ?>
                     </select>
            </div>
        </div>
        <?php }?>
       <?php if ($this->customlib->getfieldstatus('religion')) {?>
            <div class="col-md-3">
                <div class="form-group">
                    <label><?php echo $this->lang->line('religion'); ?></label><small class="req"> *</small>
                    <select id="religion" name="religion" class="form-control" autocomplete="on">
                        <option value=""><?php echo $this->lang->line('select'); ?></option>
                        <option value="Hinduism" <?php echo (set_value('religion') == 'Hinduism') ? 'selected' : ''; ?>>Hinduism</option>
                        <option value="Sikhism" <?php echo (set_value('religion') == 'Sikhism') ? 'selected' : ''; ?>>Sikhism</option>
                        <option value="Christianity" <?php echo (set_value('religion') == 'Christianity') ? 'selected' : ''; ?>>Christianity</option>
                        <option value="Buddhism" <?php echo (set_value('religion') == 'Buddhism') ? 'selected' : ''; ?>>Buddhism</option>
                        <option value="Jainism" <?php echo (set_value('religion') == 'Jainism') ? 'selected' : ''; ?>>Jainism</option>
                        <option value="Islam" <?php echo (set_value('religion') == 'Islam') ? 'selected' : ''; ?>>Islam</option>
                        <option value="Islam" <?php echo (set_value('religion') == 'Others') ? 'selected' : ''; ?>>Others</option>
                    </select>
                    <span class="text-danger"><?php echo form_error('religion'); ?></span>
                </div>
            </div>
        <?php }if ($this->customlib->getfieldstatus('cast')) {?>
                <div class="col-md-3">
                    <div class="form-group">
                        <label><?php echo $this->lang->line('caste'); ?></label>
                        <input id="cast" name="cast" placeholder="" type="text" class="form-control" autocomplete="on"  value="<?php echo set_value('cast'); ?>" />
                        <span class="text-danger"><?php echo form_error('cast'); ?></span>
                    </div>
                </div>
        <?php }?>
        <?php if ($this->customlib->getfieldstatus('is_student_house')) {
    ?>
            <div class="col-md-3 col-xs-12">
                <div class="form-group">
                    <label><?php echo $this->lang->line('house') ?></label>
                    <select class="form-control" rows="3" placeholder="" name="house">
                        <option value=""><?php echo $this->lang->line('select') ?></option>
                        <?php foreach ($houses as $hkey => $hvalue) {
        ?>
                            <option value="<?php echo $hvalue["id"] ?>" <?php if (set_value('house') == $hvalue["id"]) {  echo "selected"; } ?>><?php echo $hvalue["house_name"] ?></option>

                    <?php }?>
                    </select>
                    <span class="text-danger"><?php echo form_error('house'); ?></span>
                </div>
            </div>
            <?php
}
?>


        <?php if ($this->customlib->getfieldstatus('national_identification_no')) { ?>
              <div class="col-md-3 col-xs-12">
                <div class="form-group">
                 <label>  <?php echo $this->lang->line('national_identification_number'); ?>  </label><small class="req"> *</small>
                  <input id="adhar_no" name="adhar_no" placeholder="" type="tel" class="form-control"
                    value="<?php echo set_value('adhar_no'); ?>" autocomplete="on"
                    pattern="[0-9]{12}" maxlength="12" title="National identification number must be 12 digits"
                    oninput="this.value = this.value.replace(/[^0-9]/g, '').substring(0, 12);"
                  required />
                  <span class="text-danger"><?php echo form_error('adhar_no'); ?></span>
                 </div>
               </div>
               <div class="col-md-3">
            <div class="form-group">
                <label>Course Applying For</label><small class="req"> *</small>
                <select id="class_id" name="class_id" class="form-control" autocomplete="on">
                    <option value=""><?php echo $this->lang->line('select'); ?></option>
                    <?php foreach ($classlist as $class) { ?>
                        <option value="<?php echo $class['id'] ?>"><?php echo $class['display_name']; ?></option>
                    <?php } ?>
                </select>
                <span class="text-danger"><?php echo form_error('class_id'); ?></span>
            </div>
        </div>
        <?php } ?>


          <?php if ($this->customlib->getfieldstatus('student_height')) { ?>
             <div class="col-md-4">
                <div class="form-group">
                   <label><?php echo $this->lang->line('height'); ?></label>
                 <?php ?>
                <input type="text" name="height" class="form-control" value="<?php echo set_value('height'); ?>" autocomplete="off">
                <span class="text-danger"><?php echo form_error('height'); ?></span>
               </div>
             </div>
           <?php } ?>

    <div class="row">
        <?php if ($this->customlib->getfieldstatus('student_weight')) { ?>
            <div class="col-md-4">
                 <div class="form-group">
                    <label><?php echo $this->lang->line('weight'); ?></label>
                    <input type="text" name="weight" class="form-control" value="<?php echo set_value('weight'); ?>" autocomplete="off" >
                    <span class="text-danger"><?php echo form_error('weight'); ?></span>
               </div>
            </div>
        <?php } ?>
        <?php if ($this->customlib->getfieldstatus('measurement_date')) { ?>
            <div class="col-md-4">
                <div class="form-group">
                  <label><?php echo $this->lang->line('measurement_date'); ?></label>
                  <input type="text" id="measure_date" value="<?php echo set_value('measure_date'); ?>"  name="measure_date" class="form-control date2" autocomplete="off" readonly="" >
                  <span class="text-danger"><?php echo form_error('measure_date'); ?></span>
                </div>
            </div>
        <?php } ?>
        <?php if ($this->customlib->getfieldstatus('local_identification_no')) { ?>
             <div class="col-md-4">
                 <div class="form-group">
                    <label> <?php echo $this->lang->line('local_identification_number'); ?></label>
                     <input id="samagra_id" name="samagra_id" placeholder="" type="text" class="form-control"  value="<?php echo set_value('samagra_id'); ?>" autocomplete="off" />
                         <span class="text-danger"><?php echo form_error('samagra_id'); ?></span>
                 </div>
              </div>
        <?php } ?>

    </div>
    </div>

    <div class="row">

            <?php echo display_onlineadmission_custom_fields('students'); ?>
     </div>
    </div>

    <?php if ($this->customlib->getfieldstatus('father_name') || $this->customlib->getfieldstatus('father_phone') || $this->customlib->getfieldstatus('father_occupation') || $this->customlib->getfieldstatus('father_pic') || $this->customlib->getfieldstatus('mother_name') || $this->customlib->getfieldstatus('mother_phone') || $this->customlib->getfieldstatus('mother_occupation') || $this->customlib->getfieldstatus('mother_pic')) {?>
    <div class="printcontent">
      <div class="row">
        <h4 class="pagetitleh2">Parents' Details</h4>
       <?php if ($this->customlib->getfieldstatus('father_name')) {?>
        <div class="col-md-3">
            <div class="form-group">
                <label>Father's Name</label><small class="req"> *</small>
                <input id="father_name" name="father_name" placeholder="" type="text" class="form-control" autocomplete="off"  value="<?php echo set_value('father_name'); ?>" />
                <span class="text-danger"><?php echo form_error('father_name'); ?></span>
            </div>
        </div>
        <?php }?>
        <?php if ($this->customlib->getfieldstatus('father_phone')) {?>
        <div class="col-md-3">
            <div class="form-group">
                <label>Father's Mobile No.</label>
                <input id="father_phone" name="father_phone" placeholder="" type="tel" class="form-control" autocomplete="off" value="<?php echo set_value('father_phone'); ?>" pattern="[0-9]{10}" maxlength="10" title="Phone number must be 10 digits" />
                <span class="text-danger"><?php echo form_error('father_phone'); ?></span>
            </div>
        </div>
        <?php }?>
        <?php if ($this->customlib->getfieldstatus('father_occupation')) {?>
        <div class="col-md-3">
            <div class="form-group">
                <label>Father's Occupation</label><small class="req"> *</small>
                <input id="father_occupation" name="father_occupation" placeholder="" type="text" class="form-control"  value="<?php echo set_value('father_occupation'); ?> " autocomplete="off" />
                <span class="text-danger"><?php echo form_error('father_occupation'); ?></span>
            </div>
        </div>
        <?php }?>

        <?php if ($this->customlib->getfieldstatus('guardian_email')) {?>
        <div class="col-md-3">
            <div class="form-group">
                <label>Parent's Email</label><small class="req"> *</small>
                <input id="guardian_email" name="guardian_email" placeholder="" type="text" class="form-control"  value="<?php echo set_value('guardian_email'); ?>" autocomplete="off"/>
                <span class="text-danger"><?php echo form_error('guardian_email'); ?></span>
            </div>
        </div>
        <?php }?>

        <?php if ($this->customlib->getfieldstatus('father_pic')) {?>
            <div class="col-md-3">
                <div class="form-group">
                    <label><?php echo $this->lang->line('father_photo'); ?></label>
                    <div><input class="filestyle form-control" type='file' name='father_pic' id="file" size='20' />
                    </div>
                    <span class="text-danger"><?php echo form_error('father_pic'); ?></span></div>
            </div>
        <?php }?>
        </div><!---row-->
        <div class="row">
         <?php if ($this->customlib->getfieldstatus('mother_name')) {?>
        <div class="col-md-3">
            <div class="form-group">
                <label>Mother's Name</label><small class="req"> *</small>
                <input id="mother_name" name="mother_name" placeholder="" type="text" class="form-control"  value="<?php echo set_value('mother_name'); ?>" autocomplete="on"/>
                <span class="text-danger"><?php echo form_error('mother_name'); ?></span>
            </div>
        </div>
        <?php }?>
        <?php if ($this->customlib->getfieldstatus('mother_phone')) {?>
        <div class="col-md-3">
            <div class="form-group">
                <label>Mother's Mobile Number</label><small class="req"> *</small>
                <input id="mother_phone" name="mother_phone" placeholder="" type="tel" class="form-control" value="<?php echo set_value('mother_phone'); ?>" autocomplete="on" pattern="[0-9]{10}" maxlength="10" title="Phone number must be 10 digits"/>
                <span class="text-danger"><?php echo form_error('mother_phone'); ?></span>
            </div>
        </div>
        <?php }?>
        <?php if ($this->customlib->getfieldstatus('mother_occupation')) {?>
        <div class="col-md-3">
            <div class="form-group">
                <label>Mother's Occupation</label><small class="req"> *</small>
                <input id="mother_occupation" name="mother_occupation" placeholder="" type="text" class="form-control"  value="<?php echo set_value('mother_occupation'); ?>" autocomplete="on"/>
                <span class="text-danger"><?php echo form_error('mother_occupation'); ?></span>
            </div>
        </div>
        <?php }?>

    </div><!--./row-->
    </div><!--./printcontent-->
    <?php }?>
     <?php if ($this->customlib->getfieldstatus('if_guardian_is')) {
    ?>
    <div class="printcontent">
       <div class="row">
        <h4 class="pagetitleh2"><?php echo $this->lang->line('guardian_details'); ?></h4>
          <div class="form-group col-md-12">
            <label><?php echo $this->lang->line('if_guardian_is'); ?><small class="req"> *</small>&nbsp;&nbsp;&nbsp;</label>
            <label class="radio-inline">
                <input type="radio" name="guardian_is" <?php
echo set_value('guardian_is') == "father" ? "checked" : "";
    ?>   value="father"> <?php echo $this->lang->line('father'); ?>
            </label>
            <label class="radio-inline">
                <input type="radio" name="guardian_is" <?php
echo set_value('guardian_is') == "mother" ? "checked" : "";
    ?>   value="mother"> <?php echo $this->lang->line('mother'); ?>
            </label>
            <label class="radio-inline">
                <input type="radio" name="guardian_is" <?php
echo set_value('guardian_is') == "other" ? "checked" : "";
    ?>   value="other"> <?php echo $this->lang->line('other'); ?>
            </label>
            <span class="text-danger"><?php echo form_error('guardian_is'); ?></span>
        </div>
        <?php if ($this->customlib->getfieldstatus('guardian_name')) {?>
        <div class="col-md-3">
            <div class="form-group">
                <label><?php echo $this->lang->line('guardian_name'); ?></label><small class="req"> *</small>
                <input id="guardian_name" name="guardian_name" placeholder="" type="text" class="form-control"  value="<?php echo set_value('guardian_name'); ?>" autocomplete="off" />
                <span class="text-danger"><?php echo form_error('guardian_name'); ?></span>
            </div>
        </div>
        <?php }?>
           <?php if ($this->customlib->getfieldstatus('guardian_relation')) {?>
        <div class="col-md-3">
            <div class="form-group">
                <label><?php echo $this->lang->line('guardian_relation'); ?></label><small class="req"> *</small>
                <input id="guardian_relation" name="guardian_relation" placeholder="" type="text" class="form-control"  value="<?php echo set_value('guardian_relation'); ?>" autocomplete="off"/>
                <span class="text-danger"><?php echo form_error('guardian_relation'); ?></span>
            </div>
        </div>
        <?php }?>



    <?php if ($this->customlib->getfieldstatus('guardian_phone')) {?>
        <div class="col-md-3">
            <div class="form-group">
                <label><?php echo $this->lang->line('guardian_phone'); ?></label>
                <input id="guardian_phone" name="guardian_phone" placeholder="" type="tel" class="form-control" value="<?php echo set_value('guardian_phone'); ?>" autocomplete="off" pattern="[0-9]{10}" maxlength="10" title="Phone number must be 10 digits"/>
                <span class="text-danger"><?php echo form_error('guardian_phone'); ?></span>
            </div>
        </div>
        <?php }?>
        <?php if ($this->customlib->getfieldstatus('guardian_occupation')) {?>
        <div class="col-md-3">
            <div class="form-group">
                <label><?php echo $this->lang->line('guardian_occupation'); ?></label>
                <input id="guardian_occupation" name="guardian_occupation" placeholder="" type="text" class="form-control"  value="<?php echo set_value('guardian_occupation'); ?>" autocomplete="off" />
                <span class="text-danger"><?php echo form_error('guardian_occupation'); ?></span>
            </div>
        </div>
        <?php }?>
         <?php if ($this->customlib->getfieldstatus('guardian_address')) {?>
        <div class="col-md-6">
            <div class="form-group">
                <label><?php echo $this->lang->line('guardian_address'); ?></label>
                <textarea id="guardian_address" name="guardian_address" placeholder="" class="form-control" rows="1" autocomplete="off"><?php echo set_value('guardian_address'); ?></textarea>
                <span class="text-danger"><?php echo form_error('guardian_address'); ?></span>
            </div>
        </div>
        <?php }?>
        </div>
       </div>
       <?php }?>
       <?php if ($this->customlib->getfieldstatus('current_address') || $this->customlib->getfieldstatus('permanent_address')) {?>
    <div class="printcontent">
        <div class="row">
            <h4 class="pagetitleh2"><?php echo $this->lang->line('student_address_details'); ?></h4>
            <?php if ($this->customlib->getfieldstatus('current_address')) {?>
                <div class="col-md-6">
                 <?php if ($this->customlib->getfieldstatus('guardian_address')) {?>
                    <div class="checkbox">
                        <label> <input type="checkbox" id="autofill_current_address" onclick="return auto_fill_guardian_address();" autocomplete="off">
                        <?php echo $this->lang->line('if_guardian_address_is_current_address'); ?>
                         </label>
                    </div>
                    <?php } else {echo "<div class='checkbox'><label>&nbsp;</label></div>";}?>
                    <div class="form-group">
                        <label><?php echo $this->lang->line('current_address'); ?></label>
                        <textarea id="current_address" name="current_address" placeholder="" rows="1" class="form-control" autocomplete="off"><?php echo set_value('current_address'); ?></textarea>
                        <span class="text-danger"><?php echo form_error('current_address'); ?></span>
                    </div>
                </div>
                 <?php }if ($this->customlib->getfieldstatus('permanent_address')) {?>
                    <div class="col-md-6">
                         <?php if ($this->customlib->getfieldstatus('current_address')) {?>
                        <div class="checkbox">
                            <label> <input type="checkbox" id="autofill_address"onclick="return auto_fill_address();">
                                <?php echo $this->lang->line('if_permanent_address_is_current_address'); ?>  </label>
                         </div>
                         <?php } else {echo "<div class='checkbox'><label>&nbsp;</label></div>";}?>
                      <div class="form-group">
                            <label><?php echo $this->lang->line('permanent_address'); ?></label>
                            <textarea id="permanent_address" name="permanent_address" rows="1" placeholder="" class="form-control" autocomplete="off"></textarea>
                            <span class="text-danger"><?php echo form_error('permanent_address'); ?></span>
                        </div>
                </div>
                <?php }?>
             </div>
            </div>
             <?php }?>
             <?php if ($this->customlib->getfieldstatus('bank_account_no') || $this->customlib->getfieldstatus('bank_name') || $this->customlib->getfieldstatus('ifsc_code') || $this->customlib->getfieldstatus('rte') || $this->customlib->getfieldstatus('previous_school_details') || $this->customlib->getfieldstatus('student_note')) {
    ?>
        <div class="printcontent">
            <div class="row">
                <h4 class="pagetitleh2"><?php echo $this->lang->line('miscellaneous_details'); ?></h4>
              <?php if ($this->customlib->getfieldstatus('bank_account_no')) {?>
                <div class="col-md-4">
                    <div class="form-group">
                        <label><?php echo $this->lang->line('bank_account_number'); ?></label>
                        <input id="bank_account_no" name="bank_account_no" placeholder="" type="text" class="form-control"  value="<?php echo set_value('bank_account_no'); ?>" autocomplete="off" />
                        <span class="text-danger"><?php echo form_error('bank_account_no'); ?></span>
                    </div>
                </div>
            <?php }if ($this->customlib->getfieldstatus('bank_name')) {?>
                <div class="col-md-4">
                    <div class="form-group">
                        <label><?php echo $this->lang->line('bank_name'); ?></label>
                        <input id="bank_name" name="bank_name" placeholder="" type="text" class="form-control"  value="<?php echo set_value('bank_name'); ?>" autocomplete="off" />
                        <span class="text-danger"><?php echo form_error('bank_name'); ?></span>
                    </div>
                </div><?php }
    if ($this->customlib->getfieldstatus('ifsc_code')) {?>
                <div class="col-md-4">
                    <div class="form-group">
                        <label><?php echo $this->lang->line('ifsc_code'); ?></label>
                        <input id="ifsc_code" name="ifsc_code" placeholder="" type="text" class="form-control"  value="<?php echo set_value('ifsc_code'); ?>" autocomplete="off" />
                        <span class="text-danger"><?php echo form_error('ifsc_code'); ?></span>
                    </div>
                </div>
            <?php }?>
        </div>
        <div class="row">
            <?php if ($this->customlib->getfieldstatus('rte')) {
        ?>
                    <div class="col-md-4">
                        <label><?php echo $this->lang->line('rte'); ?></label>
                         <div class="radio" style="margin-top: 2px;">
                          <label><input class="radio-inline" type="radio" name="rte" value="Yes"  <?php
echo set_value('rte') == "yes" ? "checked" : "";
        ?>  ><?php echo $this->lang->line('yes'); ?></label>
                            <label><input class="radio-inline" checked="checked" type="radio" name="rte" value="No" <?php
echo set_value('rte') == "no" ? "checked" : "";
        ?>  ><?php echo $this->lang->line('no'); ?></label>
                                </div>
                    <span class="text-danger"><?php echo form_error('rte'); ?></span>
                </div>
                <?php }?>
        </div>
        <div class="row">
            <?php if ($this->customlib->getfieldstatus('previous_school_details')) {?>
                <div class="col-md-6">
                    <div class="form-group">
                        <label><?php echo $this->lang->line('previous_school_details'); ?></label>
                        <textarea class="form-control" rows="1" placeholder="" name="previous_school" autocomplete="off"><?php echo set_value('previous_school'); ?></textarea>
                        <span class="text-danger"><?php echo form_error('previous_school'); ?></span>
                    </div>
                </div>
                <?php }?>
                <?php if ($this->customlib->getfieldstatus('student_note')) {?>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><?php echo $this->lang->line('note'); ?></label>
                            <textarea class="form-control" rows="1" placeholder="" name="note" autocomplete="off"><?php echo set_value('note'); ?></textarea>
                            <span class="text-danger"><?php echo form_error('note'); ?></span>
                        </div>
                    </div>
                <?php }?>
            </div>
         </div>
        <?php }?>
        <?php if ($this->customlib->getfieldstatus('upload_documents')) {?>
        <div class="printcontent">
          <div class="row">
            <h4 class="pagetitleh2"><?php echo $this->lang->line('upload_documents'); ?></h4>
            <div class="col-md-6">
              <div class="form-group">
                <label>Class 10<sup>th</sup> Marksheet</label> (<small>Max Size: 1MB, Supported Files: JPG, JPEG, PNG, PDF</small>)<small class="req"> *</small>
                <input id="document" name="document"  type="file" class="form-control filestyle" required value="<?php echo set_value('document'); ?>" />
                <span class="text-danger"><?php echo form_error('document'); ?></span>
            </div>
          </div>
          <div class="col-md-6">
              <div class="form-group">
                <label>Class 11/12/UG Marksheet</label> (<small>Max Size: 1MB, Supported Files: JPG, JPEG, PNG, PDF</small>)
                <input id="document_12" name="document_12"  type="file" class="form-control filestyle" value="<?php echo set_value('document_12'); ?>" />
                <span class="text-danger"><?php echo form_error('document_12'); ?></span>
            </div>
          </div>


          <?php if ($this->customlib->getfieldstatus('mother_pic')) {?>
            <div class="col-md-6">
                <div class="form-group">
                    <label><?php echo $this->lang->line('mother_photo'); ?></label> (<small>Max File Size: 1MB, Supported Files: JPG, JPEG, PNG</small>)
                    <div><input class="filestyle form-control" type='file' name='mother_pic' id="file" size='20' />
                    </div>
                    <span class="text-danger"><?php echo form_error('mother_pic'); ?></span></div>
            </div>
       <?php }?>
         </div>
         <div class="row">
        <?php if ($this->customlib->getfieldstatus('student_photo')) {?>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Student's Photo</label> (<small>Maximum File Size: 1MB, Supported File Types: JPG, JPEG, PNG</small>)<small class="req"> *</small>
                        <div><input class="filestyle form-control" type='file' name='file' id="file" size='20' required />
                        </div>
                        <span class="text-danger"><?php echo form_error('file'); ?></span></div>
                </div>
                <?php }?>
<?php }?>
         <?php if ($this->customlib->getfieldstatus('guardian_photo')) {?>
          <div class="col-md-6">
            <div class="form-group">
                <label>Student's Signature</label> (<small>Maximum File Size: 1MB, Supported File Types: JPG, JPEG, PNG</small>)<small class="req"> *</small>
                <div><input class="filestyle form-control" type='file' name='guardian_pic' id="file" size='20' required />
                </div>
                <span class="text-danger"><?php echo form_error('guardian_pic'); ?></span></div>
        </div>
    </div>
        </div>
        <?php } ?>
            <div class="row">
                   <?php if ($is_captcha) {?>
                    <div class="col-lg-4 col-md-5 col-sm-7">
                        <div class="d-flex align-items-center">
                            <span id="captcha_image"><?php echo $this->captchalib->generate_captcha()['image']; ?></span>
                            <span class="fa fa-refresh capture-icon" title='Refresh Catpcha' onclick="refreshCaptcha()"></span>
                            <input type="text" name="captcha" placeholder="<?php echo $this->lang->line('captcha'); ?>" class=" form-control width-auto" id="captcha"  autocomplete="off">
                        </div>
                    </div>
                    <?php }?>
                    <div><br><br>
                    <div><strong>100% Safe & Secure Payments - Powered by ICICI Eazypay</strong></div>
                    <div><img src="https://my.ximi.ac.in/wp-content/uploads/2023/05/Payment-Icons-ICICI-Eazypay.png" alt="Payment Methods" width="350px"></div>
                   </div><br>
                <div class="col-lg-2 col-md-2 col-sm-5">
                    <div class="form-group <?php if ($is_captcha) {echo 'btnMD';}?> ">
                       <button type="submit" class="onlineformbtn mt10">Submit & Pay</button>
                    </div>
                </div>
                <div class="col-md-7">
                    <span class="text-danger"><?php echo form_error('captcha'); ?></span>
                </div>
            </div>
    </div><!--./row-->
</form>
<div id="checkOnlineAdmissionStatus" class="modal fade" role="dialog" tabindex="-1">
  <div class="modal-dialog">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header modal-header-small">
        <button type="button" class="close closebtnmodal" data-dismiss="modal">&times;</button>
        <h4 ><?php echo $this->lang->line('check_your_form_status') ?></h4>
      </div>
       <form action="<?php echo base_url() . 'welcome/checkadmissionstatus' ?>" method="post" class="onlineform" id="checkstatusform">
          <div class="modal-body">
            <div class="form-group">
            <label><?php echo $this->lang->line('enter_your_reference_number'); ?></label><small class="req"> *</small>
               <input type="text" class="form-control" name="refno" id="refno" autocomplete="off">
                 <span class="text-danger" id="error_status_refno"></span>
            </div>
             <div class="form-group mb10">
              <label><?php echo $this->lang->line('select_your_date_of_birth'); ?></label><small class="req"> *</small>
               <input type="text"  class="form-control date2"  name="student_dob" id="student_dob" autocomplete="off" readonly="">
                <span class="text-danger" id="error_status_dob"></span>
            </div>
             <span class="text-danger" id="invaliderror"></span>
          </div>
          <div class="modal-footer">
          <button type="button" class="modalclosebtn btn  mdbtn" data-dismiss="modal"><?php echo $this->lang->line('close'); ?></button>
          <button type="submit" class="onlineformbtn mdbtn" ><?php echo $this->lang->line('submit'); ?></button>
          </div>
      </form>
    </div>
  </div>
</div>

<script type="text/javascript">
var date_format = '<?php echo $result     = strtr($this->customlib->getSchoolDateFormat(), ['d' => 'dd', 'm' => 'mm', 'Y' => 'yyyy']) ?>';
var datetime_format = '<?php echo $result = strtr($this->customlib->getSchoolDateFormat(), ['d' => 'DD', 'm' => 'MM', 'M' => 'MMM', 'Y' => 'YYYY']) ?>';

    $(document).ready(function () {
        // console.log(datetime_format);
        var class_id = $('#class_id').val();
        var section_id = '<?php echo set_value('section_id', 0) ?>';

        getSectionByClass(class_id, section_id);

         // Check URL hash and open popup if it matches
        if (window.location.hash === "#checkOnlineAdmissionStatus") {
            // Ensure modal is properly initialized before showing
            setTimeout(function() {
                $('#checkOnlineAdmissionStatus').modal({
                    show: true,
                    backdrop: 'static',
                    keyboard: false
                });
            }, 500);
        }


        $(document).on('change', '#class_id', function (e) {
            $('#section_id').html("");
            var class_id = $(this).val();
            getSectionByClass(class_id, 0);
        });

        $('.date2').datepicker({
            autoclose: true,
             format: date_format,
            todayHighlight: true
        });
        $('.date').datepicker({
            autoclose: true,
             format: date_format,
            todayHighlight: true
        });
        $('.datetime').datetimepicker({
         format: datetime_format + ' hh:mm a',
          locale:'en'
        });

        function getSectionByClass(class_id, section_id) {

            if (class_id !== "") {
                $('#section_id').html("");

                var div_data = '';
                var url = "";

                $.ajax({
                    type: "POST",
                    url: base_url + "welcome/getSections",
                    data: {'class_id': class_id},
                    dataType: "json",
                    beforeSend: function () {
                        $('#section_id').addClass('dropdownloading');
                    },
                    success: function (data) {
                        $.each(data, function (i, obj)
                        {
                            var sel = "";
                            if (section_id === obj.section_id) {
                                sel = "selected";
                            }
                            div_data += "<option value=" + obj.id + " " + sel + ">" + obj.section + "</option>";
                        });
                        $('#section_id').append(div_data);
                    },
                    complete: function () {
                        $('#section_id').removeClass('dropdownloading');
                    }
                });
            }
        }
    });

    function auto_fill_guardian_address() {
        if ($("#autofill_current_address").is(':checked'))
        {
            $('#current_address').val($('#guardian_address').val());
        }
    }

    function auto_fill_address() {
        if ($("#autofill_address").is(':checked'))
        {
            $('#permanent_address').val($('#current_address').val());
        }
    }

    $('input:radio[name="guardian_is"]').change(
            function () {
                if ($(this).is(':checked')) {
                    var value = $(this).val();
                    if (value === "father") {
                        var father_relation = "<?php echo $this->lang->line('father'); ?>";
                        $('#guardian_name').val($('#father_name').val());
                        $('#guardian_phone').val($('#father_phone').val());
                        $('#guardian_occupation').val($('#father_occupation').val());
                        $('#guardian_relation').val(father_relation);
                    } else if (value === "mother") {
                        var mother_relation = "<?php echo $this->lang->line('mother'); ?>";
                        $('#guardian_name').val($('#mother_name').val());
                        $('#guardian_phone').val($('#mother_phone').val());
                        $('#guardian_occupation').val($('#mother_occupation').val());
                        $('#guardian_relation').val(mother_relation);
                    } else {
                        $('#guardian_name').val("");
                        $('#guardian_phone').val("");
                        $('#guardian_occupation').val("");
                        $('#guardian_relation').val("");
                    }
                }
            });
</script>

<script type="text/javascript">
    function refreshCaptcha(){
        $.ajax({
            type: "POST",
            url: "<?php echo base_url('site/refreshCaptcha'); ?>",
            data: {},
            success: function(captcha){
                $("#captcha_image").html(captcha);
            }
        });
    }
</script>

<script type="text/javascript">
$(document).ready(function(){
$(document).on('submit','#checkstatusform',function(e){
   e.preventDefault(); // avoid to execute the actual submit of the form.
    var form = $(this);
    var url = form.attr('action');
    var form_data = form.serializeArray();

    $.ajax({
           url: url,
           type: "POST",
           dataType:'JSON',
           data: form_data, // serializes the form's elements.
              beforeSend: function () {

               },
              success: function(response) { // your success handler
                if(response.status==0){

                    $.each(response.error, function(key, value) {
                    $('#error_status_' + key).html(value);
                    });
                }else if(response.status==2){
                    $('#error_status_dob' ).html("");
                    $('#error_status_refno' ).html("");
                    $('#invaliderror').html(response.error);
                } else{
                    var refno =response.refno ;
                    window.location.href="<?php echo base_url() . 'welcome/online_admission_review/' ?>"+refno ;
                }
              },
             error: function() { // your error handler

             },
             complete: function() {

             }
         });
});
});
</script>

<script>
    function openStatusFormmodal(){
      $('#error_status_dob' ).html("");
      $('#error_status_refno' ).html("");
      $('#invaliderror').html("");
      $('#student_dob').val("");
      $('#student_dob').html("");
      $('#refno' ).val("");
      $(':input').val('');
    }

    /* Commenting out duplicate functions
    function auto_fill_guardian_address() {
        if ($("#autofill_current_address").is(':checked'))
        {
            $('#current_address').val($('#guardian_address').val());
        }
    }

    function auto_fill_address() {
        if ($("#autofill_address").is(':checked'))
        {
            $('#permanent_address').val($('#current_address').val());
        }
    }
    */
</script>

<script>
$(function(){
    $('#checkOnlineAdmissionStatus').modal({
         backdrop: 'static',
         keyboard: false,
         show: false
    });
});
</script>
</div>
</div>
<!-- Move the footer outside the admission-container div, right before the closing body tag -->
<div class="footer-broodle">
    <p>© 2025 St. Xavier's College Jaipur. All Rights Reserved.</p>
    <p>Developed & Hosted by <a style="color: #ffffff;" href="https://broodle.host">Broodle</a></p>
</div>
<!-- Add this right after your class dropdown -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Convert PHP array to JavaScript array
    var classList = <?php echo json_encode($classlist); ?>;

    // console.log("All Classes:");
    classList.forEach(function(classItem) {
        // console.log({
        //     "Complete Class Name": classItem.class,
        //     "Display Name": classItem.display_name,
        //     "ID": classItem.id
        // });
    });
});
</script>

<!-- Add this JavaScript validation code right before the closing </body> tag -->
<script>
// Add validation for phone number fields - digits only and max 10 digits
document.addEventListener('DOMContentLoaded', function() {
    // Get all phone input fields
    const phoneFields = [
        document.getElementById('mobileno'),
        document.getElementById('father_phone'),
        document.getElementById('mother_phone'),
        document.getElementById('guardian_phone')
    ];

    // Add validation to each phone field
    phoneFields.forEach(function(field) {
        if (field) {
            // Restrict input to digits only and max 10 digits
            field.addEventListener('input', function() {
                // Remove any non-digit characters
                this.value = this.value.replace(/\D/g, '');

                // Limit to 10 digits
                if (this.value.length > 10) {
                    this.value = this.value.slice(0, 10);
                }
            });

            // Add validation on form submission
            field.closest('form').addEventListener('submit', function(e) {
                if (field.value && (field.value.length !== 10 || !/^\d+$/.test(field.value))) {
                    alert('Phone number must be exactly 10 digits');
                    field.focus();
                    e.preventDefault();
                }
            });
        }
    });

    // Also modify the phone fields to have the correct input type and pattern
    phoneFields.forEach(function(field) {
        if (field) {
            field.setAttribute('type', 'tel');
            field.setAttribute('pattern', '[0-9]{10}');
            field.setAttribute('maxlength', '10');
            field.setAttribute('title', 'Phone number must be 10 digits');
        }
    });

    // Add validation to ensure student mobile and parent mobile numbers are not the same
    const form = document.getElementById('form1');
    if (form) {
        form.addEventListener('submit', function(e) {
            const studentMobile = document.getElementById('mobileno');
            const fatherPhone = document.getElementById('father_phone');
            const motherPhone = document.getElementById('mother_phone');
            const guardianPhone = document.getElementById('guardian_phone');

            // Only check if student mobile has a value
            if (studentMobile && studentMobile.value) {
                // Check against father's phone
                if (fatherPhone && fatherPhone.value && studentMobile.value === fatherPhone.value) {
                    alert("Student's mobile number cannot be the same as father's phone number");
                    studentMobile.focus();
                    e.preventDefault();
                    return;
                }

                // Check against mother's phone
                if (motherPhone && motherPhone.value && studentMobile.value === motherPhone.value) {
                    alert("Student's mobile number cannot be the same as mother's phone number");
                    studentMobile.focus();
                    e.preventDefault();
                    return;
                }

                // Check against guardian's phone
                if (guardianPhone && guardianPhone.value && studentMobile.value === guardianPhone.value) {
                    alert("Student's mobile number cannot be the same as guardian's phone number");
                    studentMobile.focus();
                    e.preventDefault();
                    return;
                }
            }
        });
    }
});
</script>

<script>
// Debug the favicon path
// console.log('Favicon path: <?php echo base_url($front_setting->fav_icon); ?>');

// Force the default favicon with a more robust approach
(function() {
    function updateFavicon() {
        // Get all favicon links
        var links = document.querySelectorAll("link[rel*='icon']");

        // Remove any existing favicon links
        links.forEach(function(link) {
            link.parentNode.removeChild(link);
        });

        // Create a new link element for the default favicon
        var newLink = document.createElement('link');
        newLink.rel = 'shortcut icon';
        // Use the direct URL you provided instead of the PHP variable
        newLink.href = 'https://erp.sxcjpr.edu.in/uploads/school_content/admin_small_logo/1717778709-94394494766633915f37e5!Xaviers%20Logo%20Small%20Print%20(1).png';
        newLink.type = 'image/png';
        document.getElementsByTagName('head')[0].appendChild(newLink);

        // console.log('Favicon updated to: ' + newLink.href);
    }

    // Run immediately
    updateFavicon();

    // Also run when DOM is fully loaded
    document.addEventListener('DOMContentLoaded', updateFavicon);

    // And run when window is fully loaded
    window.addEventListener('load', updateFavicon);
})();
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to add the checkbox
    function addCheckbox() {
        // console.log('Attempting to add checkbox...');

        // Check if checkbox already exists
        if (document.getElementById('same_as_current')) {
            // console.log('Checkbox already exists');
            return;
        }

        // Find the Permanent Address heading
        const permanentAddressHeadings = Array.from(document.querySelectorAll('h4')).filter(h =>
            h.textContent.trim() === 'Permanent Address'
        );

        if (permanentAddressHeadings.length === 0) {
            // console.log('Could not find Permanent Address heading');
            return;
        }

        const permanentAddressHeading = permanentAddressHeadings[0];
        // console.log('Found Permanent Address heading:', permanentAddressHeading);

        // Create a simple checkbox with label
        const checkboxHTML = `
            <div class="form-group" style="margin-top: 10px; margin-bottom: 15px;">
                <input type="checkbox" id="same_as_current" style="margin-right: 5px;">
                <label for="same_as_current" style="font-weight: normal; margin-left: 5px;">Same as Current Address</label>
            </div>
        `;

        // Create a temporary div to hold our HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = checkboxHTML;
        const checkboxDiv = tempDiv.firstElementChild;

        // Insert after the permanent address heading
        permanentAddressHeading.parentNode.insertBefore(checkboxDiv, permanentAddressHeading.nextSibling);
        // console.log('Added checkbox after Permanent Address heading');

        // Add event listener
        document.getElementById('same_as_current').addEventListener('change', function() {
            copyAddressFields(this.checked);
        });
    }

    // Function to copy address fields using direct field mapping
    function copyAddressFields(checked) {
        if (!checked) return;

        // console.log('Copying address fields...');

        // Get all input fields
        const allInputs = document.querySelectorAll('input.form-control, select.form-control, textarea.form-control');
        // console.log(`Found ${allInputs.length} total form fields`);

        // Find the Current Address and Permanent Address sections
        const currentAddressHeadings = Array.from(document.querySelectorAll('h4')).filter(h =>
            h.textContent.trim() === 'Current Address'
        );

        const permanentAddressHeadings = Array.from(document.querySelectorAll('h4')).filter(h =>
            h.textContent.trim() === 'Permanent Address'
        );

        if (currentAddressHeadings.length === 0 || permanentAddressHeadings.length === 0) {
            // console.log('Could not find address headings');
            return;
        }

        const currentAddressHeading = currentAddressHeadings[0];
        const permanentAddressHeading = permanentAddressHeadings[0];

        // console.log('Current Address heading:', currentAddressHeading);
        // console.log('Permanent Address heading:', permanentAddressHeading);

        // Find all form groups after each heading
        const currentAddressFields = [];
        const permanentAddressFields = [];

        // Helper function to get all form groups after a heading until the next heading
        function getFieldsAfterHeading(heading) {
            const fields = [];
            let currentElement = heading.parentNode;

            // Go up to find a suitable container
            while (currentElement && !currentElement.classList.contains('row')) {
                currentElement = currentElement.parentNode;
            }

            if (!currentElement) {
                // console.log('Could not find row container for', heading);
                return fields;
            }

            // Find all input fields in this row
            const inputs = currentElement.querySelectorAll('input.form-control, select.form-control, textarea.form-control');
            inputs.forEach(input => fields.push(input));

            // console.log(`Found ${fields.length} fields after heading:`, heading.textContent);
            return fields;
        }

        // Get fields for each section
        const currentFields = getFieldsAfterHeading(currentAddressHeading);
        const permanentFields = getFieldsAfterHeading(permanentAddressHeading);

        // console.log('Current address fields:', currentFields);
        // console.log('Permanent address fields:', permanentFields);

        // Copy values from current to permanent
        const minLength = Math.min(currentFields.length, permanentFields.length);

        if (minLength === 0) {
            // console.log('No fields found to copy');

            // Try a more direct approach using field IDs
            // Based on the debug logs, we can see field IDs like custom_fields[students][31]

            // Map of current address field IDs to permanent address field IDs
            // This is an educated guess based on the debug logs - adjust as needed
            const fieldMappings = [
                // Address Line 1
                { current: 'custom_fields[students][1]', permanent: 'custom_fields[students][31]' },
                // Address Line 2
                { current: 'custom_fields[students][2]', permanent: 'custom_fields[students][32]' },
                // City
                { current: 'custom_fields[students][3]', permanent: 'custom_fields[students][33]' },
                // State
                { current: 'custom_fields[students][4]', permanent: 'custom_fields[students][34]' },
                // Country
                { current: 'custom_fields[students][5]', permanent: 'custom_fields[students][35]' },
                // Pincode/Zipcode
                { current: 'custom_fields[students][6]', permanent: 'custom_fields[students][36]' }
            ];

            // Try all possible field IDs from 1-30 for current and 31-60 for permanent
            for (let i = 1; i <= 30; i++) {
                const currentId = `custom_fields[students][${i}]`;
                const permanentId = `custom_fields[students][${i + 30}]`;

                const currentField = document.getElementById(currentId);
                const permanentField = document.getElementById(permanentId);

                if (currentField && permanentField) {
                    // console.log(`Found matching fields: ${currentId} -> ${permanentId}`);
                    permanentField.value = currentField.value;

                    // Trigger change event
                    const event = new Event('change', { bubbles: true });
                    permanentField.dispatchEvent(event);

                    // console.log(`Copied value: ${currentField.value} to ${permanentField.value}`);
                }
            }

            // Also try the specific mappings
            fieldMappings.forEach(mapping => {
                const currentField = document.getElementById(mapping.current);
                const permanentField = document.getElementById(mapping.permanent);

                if (currentField && permanentField) {
                    // console.log(`Copying from ${mapping.current} to ${mapping.permanent}`);
                    permanentField.value = currentField.value;

                    // Trigger change event
                    const event = new Event('change', { bubbles: true });
                    permanentField.dispatchEvent(event);
                }
            });
        } else {
            // Copy values from the fields we found
            for (let i = 0; i < minLength; i++) {
                permanentFields[i].value = currentFields[i].value;

                // Trigger change event
                const event = new Event('change', { bubbles: true });
                permanentFields[i].dispatchEvent(event);

                // console.log(`Copied field ${i+1}: ${currentFields[i].value} to ${permanentFields[i].value}`);
            }
        }

        // console.log('Finished copying address fields');
    }

    // Try to add the checkbox immediately
    addCheckbox();

    // Also try after a delay in case the form loads dynamically
    setTimeout(addCheckbox, 1000);
});
</script>

<script>
function debugFormStructure() {
    // console.log('Debugging form structure...');

    // Log all potential headings
    const allHeadings = document.querySelectorAll('.control-label, h4, label, .form-group > strong');
    // console.log('Potential headings:', allHeadings);

    // Log specifically address-related headings
    const addressHeadings = Array.from(allHeadings).filter(
        el => el.textContent.includes('Address')
    );
    // console.log('Address headings:', addressHeadings);

    // For each address heading, log its container and fields
    addressHeadings.forEach(heading => {
        // console.log(`Heading: "${heading.textContent}"`);
        // console.log('Parent element:', heading.parentNode);
        // console.log('Closest form-group:', heading.closest('.form-group'));

        // Log nearby input fields
        const nearbyFields = Array.from(heading.closest('.form-group')?.querySelectorAll('input, textarea, select') || []);
        // console.log('Fields in same form-group:', nearbyFields);

        // Log fields in next siblings
        if (heading.closest('.form-group')?.nextElementSibling) {
            const siblingFields = heading.closest('.form-group').nextElementSibling.querySelectorAll('input, textarea, select');
            // console.log('Fields in next sibling:', siblingFields);
        }
    });
}

// Call this function when the DOM is loaded
document.addEventListener('DOMContentLoaded', debugFormStructure);
// Also try after a delay in case of dynamic loading
setTimeout(debugFormStructure, 2000);
</script>
<!-- Add this style for error highlighting -->
<style>
    .form-group.has-error .form-control {
        border-color: #a94442;
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    }

    .form-group.has-error label {
        color: #a94442;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to calculate percentage
    function calculatePercentage() {
        // Get the input elements by their IDs
        var obtainedMarksField = document.getElementById('custom_fields[students][49]');
        var totalMarksField = document.getElementById('custom_fields[students][48]');
        var percentageField = document.getElementById('custom_fields[students][50]');

        // If all fields exist
        if (obtainedMarksField && totalMarksField && percentageField) {
            // Add input event listeners to obtained marks and total marks fields
            obtainedMarksField.addEventListener('input', updatePercentage);
            totalMarksField.addEventListener('input', updatePercentage);

            // Also calculate on page load in case fields are pre-filled
            updatePercentage();

            // Function to update the percentage field
            function updatePercentage() {
                var obtainedMarks = parseFloat(obtainedMarksField.value) || 0;
                var totalMarks = parseFloat(totalMarksField.value) || 1; // Avoid division by zero

                // Only calculate if both fields have valid numbers
                if (obtainedMarks > 0 && totalMarks > 0) {
                    var percentage = (obtainedMarks / totalMarks) * 100;

                    // Round to 2 decimal places and update the percentage field
                    percentageField.value = percentage.toFixed(2);
                }
            }
        }
    }

    // Initial call
    calculatePercentage();

    // Since these fields might be loaded dynamically, check again after a delay
    setTimeout(calculatePercentage, 1000);

    // Additionally, check for these fields when any change happens to the form
    // This handles cases where the fields might be added to the DOM after initial load
    var observer = new MutationObserver(function(mutations) {
        // Check if our fields exist now
        if (document.getElementById('custom_fields[students][49]') &&
            document.getElementById('custom_fields[students][48]') &&
            document.getElementById('custom_fields[students][50]')) {
            calculatePercentage();
        }
    });

    // Observe the form for any changes
    var form = document.getElementById('form1');
    if (form) {
        observer.observe(form, { childList: true, subtree: true });
    }
});
</script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to set up percentage calculation for a specific set of fields
    function setupPercentageCalculation(obtainedMarksId, totalMarksId, percentageId, setName) {
        // Find the fields by name attribute
        var obtainedMarksField = document.querySelector(`input[name="custom_fields[students][${obtainedMarksId}]"]`);
        var totalMarksField = document.querySelector(`input[name="custom_fields[students][${totalMarksId}]"]`);
        var percentageField = document.querySelector(`input[name="custom_fields[students][${percentageId}]"]`);

        // Check if all fields exist
        if (obtainedMarksField && totalMarksField && percentageField) {
            // Make percentage field read-only
            percentageField.setAttribute('readonly', 'readonly');

            // Function to calculate and update percentage
            function updatePercentage() {
                var obtainedMarks = parseFloat(obtainedMarksField.value) || 0;
                var totalMarks = parseFloat(totalMarksField.value) || 1; // Avoid division by zero

                if (obtainedMarks > 0 && totalMarks > 0) {
                    var percentage = (obtainedMarks / totalMarks) * 100;

                    // Round to 2 decimal places
                    percentageField.value = percentage.toFixed(2);

                    // Trigger change event
                    var event = new Event('change', { bubbles: true });
                    percentageField.dispatchEvent(event);

                    // Check if this is the field we need to validate (percentageId 62)
                    if (percentageId === '62') {
                        validateEligibility(percentage);
                    }
                }
            }

            // Add input event listeners to both fields
            obtainedMarksField.addEventListener('input', function() {
                // Remove any existing error messages before updating
                const existingErrors = document.querySelectorAll('.eligibility-error');
                existingErrors.forEach(error => error.remove());
                
                updatePercentage();
                validateEligibility(parseFloat(percentageField.value) || 0);
            });

            totalMarksField.addEventListener('input', function() {
                // Remove any existing error messages before updating
                const existingErrors = document.querySelectorAll('.eligibility-error');
                existingErrors.forEach(error => error.remove());
                
                updatePercentage();
                validateEligibility(parseFloat(percentageField.value) || 0);
            });

            // Initial calculation if fields already have values
            updatePercentage();

            return true;
        }
        return false;
    }

    // Function to validate eligibility based on percentage
    function validateEligibility(percentage) {
        const submitButton = document.querySelector('button[type="submit"]');
        const percentageField = document.querySelector('input[name="custom_fields[students][62]"]');
        const obtainedMarksField = document.querySelector('input[name="custom_fields[students][78]"]');
        
        // Safety check - if any required elements are missing, exit gracefully
        if (!submitButton || !percentageField || !obtainedMarksField) {
            console.log('Required elements not found for validation');
            return;
        }

        const formGroup = percentageField.closest('.form-group');
        if (!formGroup) {
            console.log('Form group not found for validation');
            return;
        }
        
        // Remove any existing error messages from the entire form
        const existingErrors = document.querySelectorAll('.eligibility-error');
        existingErrors.forEach(error => error.remove());

        // Only proceed with validation if obtained marks field has a value
        if (!obtainedMarksField.value) {
            // Enable submit button if no obtained marks
            submitButton.disabled = false;
            submitButton.style.opacity = '1';
            submitButton.style.cursor = 'pointer';
            return;
        }

        // Create error message element
        const errorDiv = document.createElement('div');
        errorDiv.className = 'eligibility-error';
        errorDiv.style.color = 'red';
        errorDiv.style.marginTop = '10px';
        errorDiv.style.marginBottom = '10px';
        errorDiv.style.padding = '10px';
        errorDiv.style.backgroundColor = '#fff3f3';
        errorDiv.style.border = '1px solid #ffcdd2';
        errorDiv.style.borderRadius = '4px';
        errorDiv.style.width = '100%';
        errorDiv.style.textAlign = 'center';

        // Ensure percentage is a valid number
        percentage = parseFloat(percentage) || 0;

        if (percentage < 50) {
            // Disable submit button
            submitButton.disabled = true;
            submitButton.style.opacity = '0.5';
            submitButton.style.cursor = 'not-allowed';

            // Add error message
            errorDiv.textContent = 'You are not eligible for admission.';
            // Insert error message after the form group
            formGroup.parentNode.insertBefore(errorDiv, formGroup.nextSibling);
        } else {
            // Enable submit button
            submitButton.disabled = false;
            submitButton.style.opacity = '1';
            submitButton.style.cursor = 'pointer';
        }
    }

    // Function to initialize all percentage calculations
    function initializeAllCalculations() {
        // Set up calculations for both sets of fields
        var set1Ready = setupPercentageCalculation(49, 48, 50, 'Set 1');
        var set2Ready = setupPercentageCalculation(78, 60, 62, 'Set 2');

        // Add direct event listener to percentage field for Set 2
        const percentageField = document.querySelector('input[name="custom_fields[students][62]"]');
        if (percentageField) {
            percentageField.addEventListener('input', function() {
                validateEligibility(parseFloat(this.value) || 0);
            });
        }

        return set1Ready && set2Ready;
    }

    // Try to initialize immediately
    var allFieldsReady = initializeAllCalculations();

    // If not all fields are ready, check periodically
    if (!allFieldsReady) {
        var checkInterval = setInterval(function() {
            allFieldsReady = initializeAllCalculations();

            if (allFieldsReady) {
                clearInterval(checkInterval);
            }
        }, 1000); // Check every second

        // Stop checking after 10 seconds to avoid infinite searching
        setTimeout(function() {
            clearInterval(checkInterval);
        }, 10000);
    }

    // Also set up a mutation observer to detect when fields are added dynamically
    var observer = new MutationObserver(function(mutations) {
        if (!allFieldsReady) {
            allFieldsReady = initializeAllCalculations();
        }
    });

    // Observe the form for changes
    var form = document.getElementById('form1');
    if (form) {
        observer.observe(form, { childList: true, subtree: true });
    }
});
</script>

<!-- Add this script right before the closing </body> tag -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // console.log('First letter capitalization script loaded');

    // Function to only capitalize first letter if not already capitalized
    function capitalizeFirstLetter(string) {
        if (!string || typeof string !== 'string' || string.length === 0) return string;
        // Only capitalize first letter and keep the rest as is
        return string.charAt(0).toUpperCase() + string.slice(1);
    }

    // Get all text input fields
    const textInputs = document.querySelectorAll('input[type="text"], textarea');
    // console.log(`Found ${textInputs.length} text input fields to format`);

    // Process each text field
    textInputs.forEach(function(input) {
        // Skip fields that shouldn't be modified (like email)
        if (input.id === 'email' || input.id === 'guardian_email' ||
            input.name === 'email' || input.name === 'guardian_email' ||
            input.type === 'number' || input.type === 'tel' ||
            input.type === 'password' || input.type === 'date') {
            // console.log(`Skipping field: ${input.id || input.name}`);
            return;
        }

        // console.log(`Setting up first letter capitalization for field: ${input.id || input.name}`);

        // Add event listener for blur (when leaving the field)
        input.addEventListener('blur', function() {
            const originalValue = this.value;
            // Only capitalize if the value has actually changed
            if (originalValue && originalValue !== capitalizeFirstLetter(originalValue)) {
                this.value = capitalizeFirstLetter(originalValue);
                // console.log(`Capitalized first letter: "${originalValue}" to "${this.value}"`);
            }
        });

        // Also format initial values
        if (input.value) {
            const originalValue = input.value;
            input.value = capitalizeFirstLetter(originalValue);
            // console.log(`Initial capitalization: "${originalValue}" to "${input.value}"`);
        }
    });

    // Add formatting before form submission
    const form = document.getElementById('form1');
    if (form) {
        form.addEventListener('submit', function() {
            // console.log('Form submission: capitalizing first letters');
            textInputs.forEach(function(input) {
                // Skip fields that shouldn't be modified
                if (input.id === 'email' || input.id === 'guardian_email' ||
                    input.name === 'email' || input.name === 'guardian_email' ||
                    input.type === 'number' || input.type === 'tel' ||
                    input.type === 'password' || input.type === 'date') {
                    return;
                }

                input.value = capitalizeFirstLetter(input.value);
            });
        });
    }
});
</script>

 <script>
document.addEventListener('DOMContentLoaded', function() {
    // More precise mapping of courses to their subject combinations
    const courseToSubjectMapping = {
        'B.A. (English)': {
            fieldIds: ['80'],
            displayName: 'BA English Subject Combinations'
        },
        'B.A. (Economics)': {
            fieldIds: ['81'],
            displayName: 'BA Economics Subject Combinations'
        },
        'B.A. (Political Science)': {
            fieldIds: ['82'],
            displayName: 'BA Political Science Subject Combinations'
        },
        'B.A. (Psychology)': {
            fieldIds: ['83'],
            displayName: 'BA Psychology Subject Combinations'
        },
        'B.A.': {
            fieldIds: ['79', '85', '86'],
            displayName: 'BA Subject Combinations'
        },
        'B.SC.': {
            fieldIds: ['84' , '87'],
            displayName: 'BSC Subject Combinations'
        }
    };

    // Function to find custom field container by field ID
    function findCustomFieldContainer(fieldId) {
        return document.querySelector(`[name="custom_fields[students][${fieldId}]"]`)?.closest('.form-group');
    }

    // Function to hide all subject combination fields
    function hideAllSubjectFields() {
        Object.values(courseToSubjectMapping).forEach(({fieldIds}) => {
            fieldIds.forEach(fieldId => {
                const container = findCustomFieldContainer(fieldId);
                if (container) {
                    container.style.display = 'none';

                    // Clear the field value and remove required attribute when hiding
                    const field = container.querySelector('select, input');
                    if (field) {
                        field.value = '';
                        field.removeAttribute('required');
                        field.dispatchEvent(new Event('change', { bubbles: true }));
                    }

                    // Remove required indicator from label
                    const label = container.querySelector('label');
                    if (label) {
                        label.innerHTML = label.innerHTML.replace('<small class="req"> *</small>', '');
                    }

                    // Reset column classes
                    const parentCol = container.closest('[class*="col-"]');
                    if (parentCol) {
                        parentCol.className = ''; // Clear all column classes
                        parentCol.classList.add('col-md-12'); // Default to full width
                    }
                }
            });
        });
    }

    // Function to show specific subject combination fields
    function showSubjectFields(fieldIds) {
        fieldIds.forEach((fieldId, index) => {
            const container = findCustomFieldContainer(fieldId);
            if (container) {
                container.style.display = 'block';

                // Add required attribute to the field
                const field = container.querySelector('select, input');
                if (field) {
                    field.setAttribute('required', 'required');
                }

                // Add required indicator to label
                const label = container.querySelector('label');
                if (label && !label.innerHTML.includes('*')) {
                    label.innerHTML += '<small class="req"> *</small>';
                }

                // Adjust column width based on number of fields
                const parentCol = container.closest('[class*="col-"]');
                if (parentCol) {
                    parentCol.className = ''; // Clear all column classes
                    if (fieldIds.length > 1) {
                        // For multiple fields, use 4 columns each
                        parentCol.classList.add('col-md-4');
                        // Add margin for spacing
                        container.style.marginBottom = '15px';
                    } else {
                        // Single field takes full width
                        parentCol.classList.add('col-md-6');
                    }
                }
            }
        });
    }

    // Function to handle course selection change
    function handleCourseSelection(selectedCourse) {
        hideAllSubjectFields();

        // Only show fields if there's an exact match
        const exactMatch = courseToSubjectMapping[selectedCourse];
        if (exactMatch) {
            showSubjectFields(exactMatch.fieldIds);
        }
        // Remove the partial matching logic to prevent incorrect field display
    }

    // Add change event listener to class dropdown
    const classDropdown = document.getElementById('class_id');
    if (classDropdown) {
        classDropdown.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const selectedCourse = selectedOption.text.trim();
            handleCourseSelection(selectedCourse);
        });

        // Handle initial state if course is pre-selected
        if (classDropdown.value) {
            classDropdown.dispatchEvent(new Event('change'));
        }
    }

    // Initial setup - hide all fields
    hideAllSubjectFields();
});
</script>


                            <script>
                              document.addEventListener('DOMContentLoaded', function() {
                                    var headings = document.getElementsByTagName('h4');
                                    for(var i = 0; i < headings.length; i++) {
                                        if(headings[i].textContent.trim() === 'Subject Combinations') {
                                            headings[i].style.display = 'none';
                                            break;
                                        }
                                    }
                                });
                            </script>
