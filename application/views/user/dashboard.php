<div class="content-wrapper">
    <section class="content pb0">
    	<div class="row">
    		<div class="col-lg-6 col-md-6 col-sm-12">
	    		<div class="box box-primary borderwhite">
	                <div class="box-body">
	                	<div class="row">
	                		<div class="col-lg-3 col-md-3 col-sm-3">
								<?php 
									if (!empty($student_data["image"])) {
                                        $file = base_url() . $student_data["image"].img_time();
                                    } else {                            
                                        if ($student_data['gender'] == 'Female') {
                                            $file = base_url() . "uploads/student_images/default_female.jpg".img_time();
                                        } else {
                                            $file = base_url() . "uploads/student_images/default_male.jpg".img_time();
                                        }
                                    }
								?>
							
	                			<img src="<?php echo $file.''.img_time(); ?>" class="img-rounded img-responsive img-h-150 mb-xs-1">						
								
	                		</div><!--./col-lg-3-->
	                		<div class="col-lg-9 col-md-9 col-sm-9">
	                			<h4 class="mt0"><?php echo $this->lang->line('welcome'); ?>, <?php echo $studentsession_username; ?></h4>
	                			
	                			<!-- Single Combined Attendance Table -->
	                			<div class="table-responsive">
	                				<h5><?php echo $this->lang->line('attendance_overview'); ?></h5>
	                				<table class="table table-bordered table-hover attendance-overview">
	                					<thead>
	                						<tr>
	                							<th class="text-center"><?php echo $this->lang->line('present'); ?></th>
	                							<th class="text-center"><?php echo $this->lang->line('absent'); ?></th>
	                							<th class="text-center"><?php echo $this->lang->line('pod'); ?></th>
	                							<th class="text-center"><?php echo $this->lang->line('total'); ?></th>
	                							<th class="text-center"><?php echo $this->lang->line('percentage'); ?></th>
	                						</tr>
	                					</thead>
	                					<tbody>
	                						<?php
	                						// Calculate Combined Attendance
	                						$total_present = $total_absent = $total_pod = 0;

	                						// Add Subject Attendance
	                						foreach ($attendance_data['subject_attendance'] as $record) {
	                							switch ($record['attendence_type_id']) {
	                								case 1: $total_present += $record['count']; break;
	                								case 4: $total_absent += $record['count']; break;
	                								case 6: $total_pod += $record['count']; break;
	                							}
	                						}

	                						// Add Extra Attendance
	                						foreach ($attendance_data['extra_attendance'] as $record) {
	                							switch ($record['attendence_type_id']) {
	                								case 1: $total_present += $record['count']; break;
	                								case 4: $total_absent += $record['count']; break;
	                								case 6: $total_pod += $record['count']; break;
	                							}
	                						}

	                						// Add Alternate Attendance
	                						foreach ($attendance_data['alternate_attendance'] as $record) {
	                							switch ($record['attendence_type_id']) {
	                								case 1: $total_present += $record['count']; break;
	                								case 4: $total_absent += $record['count']; break;
	                								case 6: $total_pod += $record['count']; break;
	                							}
	                						}

	                						$total_classes = $total_present + $total_absent + $total_pod;
	                						$total_percentage = $total_classes > 0 ? 
	                							round((($total_present + $total_pod) / $total_classes) * 100, 2) : 0;
	                							?>
	                						<tr>
	                							<td class="text-center"><?php echo $total_present; ?></td>
	                							<td class="text-center"><?php echo $total_absent; ?></td>
	                							<td class="text-center"><?php echo $total_pod; ?></td>
	                							<td class="text-center"><?php echo $total_classes; ?></td>
	                							<td class="text-center"><?php echo $total_percentage; ?>%</td>
	                						</tr>
	                					</tbody>
	                				</table>
	                			</div>

	                			<?php if($total_percentage > 0 && $total_percentage < $low_attendance_limit && $low_attendance_limit != '0.00' ){ ?>
	                				<p class="text-danger"><?php echo $this->lang->line('your_current_attendance_is'); ?> <?php echo $total_percentage; ?>% <?php echo $this->lang->line('which_is_lower_than');?> <?php echo $low_attendance_limit; ?>% <?php echo $this->lang->line('of_minimum_attendance_mark'); ?>. </p>				
	                			<?php } elseif($total_percentage > 0 && $total_percentage >= $low_attendance_limit && $low_attendance_limit != '0.00'){ ?>
	                				<p class="text-success"><?php echo $this->lang->line('your_current_attendance_is'); ?> <?php echo $total_percentage; ?>% <?php echo $this->lang->line('which_is_above'); ?> <?php echo $low_attendance_limit; ?>% <?php echo $this->lang->line('of_minimum_attendance_mark'); ?>.</p>	
	                			<?php } ?>
	                		 
	                		</div><!--./col-lg-9-->
	                	</div><!--./row-->
	                </div>
	            </div>   
	        </div><!--./col-lg-6-->
			<div class="col-lg-6 col-md-6 col-sm-12">	    		 
				<div class="box box-primary borderwhite">
                    <div class="box-header with-border">
                        <h3 class="box-title"><?php echo $this->lang->line('notice_board'); ?></h3>      
                    </div>
                    <div class="box-body pb0">
						<?php if(!empty($notificationlist)){ ?>
                    	<ul class="user-progress ps mb0">
                    		<?php for($i=0;$i<4;$i++){
                    			$notification = array();
                    			if(!empty($notificationlist[$i])){
	                    			$notification=$notificationlist[$i];
                                }
	                    	?>
	                    <?php if(!empty($notification)){ ?>
			                <li class="doc-file-type">			                   
				                <div class="set-flex">
					                <div class="media-title"><?php if(!empty($notification)){ ?>
									<a href="<?php echo base_url(); ?>user/notification" class="displayinline text-muted" target="_blank">
									
					                	<?php if ($notification['notification_id'] == "read") { ?>
                                            <img src="<?php echo base_url() ?>/backend/images/read_one.png">
                                        <?php } else { ?>
                                            <img src="<?php echo base_url() ?>backend/images/unread_two.png">
                                        <?php }?>
										
										&nbsp;<?php  echo $notification['title']; ?> (<?php if(!empty($notification)){ echo "<i class='fa fa-clock-o text-aqua'></i>". ' '. date($this->customlib->getSchoolDateFormat(), $this->customlib->dateyyyymmddTodateformat($notification['date']));} ?>)
					                </a><?php } ?>
									</div>                

			            		</div>   
				               
			                </li><!-- /.item -->
			            <?php } } ?>
			                
			            </ul>  
						<?php }else{ ?>
							<img src="https://smart-school.in/ssappresource/images/addnewitem.svg"  width="150" class="center-block mt20">
						<?php } ?>
                    </div>                   
                </div>
			</div><!--./col-lg-6-->  
    	</div><!--./row-->	
		 
		<div class="row">
			<div class="col-lg-6 col-md-6 col-sm-12">
				<div class="box box-primary borderwhite">
                    <div class="box-header with-border">
                        <h3 class="box-title"><?php echo $this->lang->line('subject_progress'); ?></h3>      
                    </div>
                    <div class="box-body direct-chat-messages">
                    	<div class="table-responsive">
							<?php   if (!empty($subjects_data)) {  ?>
                    		<table class="table table-striped table-hover">
                    			<tr class="active">
                    				<th><?php echo $this->lang->line('subject'); ?></th>
                    				<th><?php echo $this->lang->line('progress'); ?></th>
                    				<!-- <th>Duration</th> -->
                    			</tr>
                    		<?php 
                                    foreach ($subjects_data as $key => $value) {
                            ?>
                    			<tr>
                    				<td><?php echo $value['lebel']; ?></td>
                    				<td><?php echo $value['complete']; ?>%
                    					<div class="progress progress-minibar">
										  <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow=""
										  aria-valuemin="0" aria-valuemax="100" style="width:<?php if($value['complete'] !=0){ echo $value['complete'];} ?>%">
										  </div>
										</div>
                    				</td>
                    				<!-- <td>2 Months</td> -->
                    			</tr>
                    		<?php }  ?>
                    			
                    		</table>
							<?php }else{  ?>
								<img src="https://smart-school.in/ssappresource/images/addnewitem.svg"  width="150" class="center-block mt20">
							<?php } ?>
                    	</div>
                    </div>
                </div>
			</div><!--./col-lg-4-->

			<div class="col-lg-6 col-md-6 col-sm-12">
				<div class="box box-primary borderwhite">
                    <div class="box-header with-border">
                        <h3 class="box-title"><?php echo $this->lang->line('upcomming_class'); ?></h3>      
                    </div>
                    <div class="box-body direct-chat-messages">					 
					
						<?php if (!empty($timetable)) { ?>
                    	<ul class="user-progress">

                    	<?php 
                    		foreach ($timetable as $tm_key => $tm_value) {

                    			if (!$timetable[$tm_key]) {
                    	 ?>
			            <?php }else{ 
                                for($i=0;$i<5;$i++){

	                                $timetablelist = array();
	                    			if(!empty($timetable[$tm_key][$i])){
	                    				
		                    			$timetablelist=$timetable[$tm_key][$i];
		                 
	                                }
 
			             if(!empty($timetablelist)){ ?>
			            	<li class="lecture-list">

			            		<?php $profile_pic = '';
			            		if($timetablelist->image !=''){
				            	    $profile_pic = 'uploads/staff_images/'.$timetablelist->image;
				            	}else{
				            	    if($timetablelist->gender == 'Male'){
	                                    $profile_pic = 'uploads/staff_images/default_male.jpg';
				            		 }else{
	                                    $profile_pic = 'uploads/staff_images/default_female.jpg';
				            		} 
			            		}?>
			                    <img src="<?php echo base_url(); ?><?php echo $profile_pic.img_time(); ?>" alt="" class="img-circle msr-3 object-fit-cover fit-image-40" width="40" height="40">

				                <div class="set-flex">
					                <div class="media-title bmedium"><?php echo $timetablelist->name.' '.$timetablelist->surname.' (' . $timetablelist->employee_id .')'; ?> 
					                </div>
					                <div class="text-muted mb0">
					                	<?php
					                	if(!empty($timetablelist)){
                                            echo $timetablelist->subject_name;
                                            if ($timetablelist->code != '') {
                                                echo " (" . $timetablelist->code . ")";
                                            }
                                        }
                                        ?>                          	
                                    </div>
			            		</div>    
				                 <div class="ms-auto">
					                <div class="bmedium"><?php echo $this->lang->line('room_no'); ?>:<?php echo $timetablelist->room_no; ?></div>
					                <div class="text-muted mb0"><?php echo $timetablelist->time_from ?>-<?php echo $timetablelist->time_to; ?></div>
				                 </div>
			                </li>
			           <?php } } } }  ?>
			               
			            </ul>
						<?php }else{  ?>
							<img src="https://smart-school.in/ssappresource/images/addnewitem.svg"  width="150" class="center-block mt20">
						<?php } ?>
                    </div>
                </div>
			</div><!--./col-lg-4-->

			<div class="col-lg-6 col-md-6 col-sm-12">
				<div class="box box-primary borderwhite">
                    <div class="box-header with-border">
                        <h3 class="box-title"><?php echo $this->lang->line('homework'); ?></h3>      
                    </div>

                    <div class="box-body direct-chat-messages">
                    	
						<?php if(!empty($homeworklist)){ ?>
                    	<ul class="user-progress ps">
                    		<?php for($i=0;$i<5;$i++){
                    			$homework = array();
                    			if(!empty($homeworklist[$i])){
	                    			$homework=$homeworklist[$i];
                                }
	                    	?>
	                    <?php if(!empty($homework)){ ?>
			                <li class="doc-file-type">
				                <div class="set-flex">
					                <div class="media-title font-16"><?php if(!empty($homework)){ ?><a href="<?php echo base_url(); ?>user/homework" class="displayinline text-muted" target="_blank"><?php  echo $homework['subject_name']; ?> (<?php  echo $homework['subject_code']; ?>)									
									</a><?php } ?></div>
					                <div class="text-muted mb0"><?php if(!empty($homework)){ echo $this->lang->line('homework_date').': '. date($this->customlib->getSchoolDateFormat(), $this->customlib->dateyyyymmddTodateformat($homework['homework_date'])) .',';} ?> <?php if(!empty($homework)){ echo $this->lang->line('submission_date'). ': '. date($this->customlib->getSchoolDateFormat(), $this->customlib->dateyyyymmddTodateformat($homework['submit_date'])) .',';} ?> <?php if(!empty($homework)){

                                        if ($homework["status"] == 'submitted') {
                                            $status_class = "class= 'label label-warning'";
                                            $status_homework = $this->lang->line('submitted');
                                        }else{
                                            $status_class = "class= 'label label-danger'";
                                            $status_homework = $this->lang->line("pending");
                                        }
                      
                                        if ($homework["homework_evaluation_id"] != 0) {
                                           
                                            $status_class = "class= 'label label-success'";
                                            $status_homework = $this->lang->line("evaluated");
                                        }

                                        echo $this->lang->line('status').': ';
                                        ?>
                                        <label <?php echo $status_class; ?>><?php echo $status_homework; ?></label>
								    <?php	
								    }
									?>

					            </div>
			            		</div> 
			                </li><!-- /.item -->
			            <?php } } ?>			                
			            </ul> 
						<?php }else{ ?>
							<img src="https://smart-school.in/ssappresource/images/addnewitem.svg"  width="150" class="center-block mt20">
						<?php } ?>
                    </div>
                </div>
			</div><!--./col-lg-4-->	
			
			
			<div class="col-lg-6 col-md-6 col-sm-12">
				<div class="box box-primary borderwhite">
                    <div class="box-header with-border">
                        <h3 class="box-title"><?php echo $this->lang->line('teacher_list'); ?></h3>      
                    </div>

                    <div class="box-body direct-chat-messages">                    	
						<?php  					 
						 
							if(!empty($teacherlist)){   
								
						?>
                    	<ul class="user-progress ps">
                    		<?php foreach ($teacherlist as $teacher) {							 
								
								$class_teacher = '';
							
								if ($teacher[0]->class_teacher == $teacher[0]->staff_id) {
									$class_teacher = '<span class="label label-success bolds">' . $this->lang->line('class_teacher') . '</span>' ;
								}
							?>
							<li class="lecture-list">

			            		<?php 
										$profile_pic = '';
										
										if($teacher[0]->image !=''){
											$profile_pic = 'uploads/staff_images/'.$teacher[0]->image;
										}else{
											if($teacher[0]->gender == 'Male'){
												$profile_pic = 'uploads/staff_images/default_male.jpg';
											}else{
												$profile_pic = 'uploads/staff_images/default_female.jpg';
											} 
										}
								?>
			                    <img src="<?php echo base_url(); ?><?php echo $profile_pic.img_time(); ?>" alt="" class="img-circle msr-3 object-fit-cover fit-image-40" width="40" height="40">

				                <div class="set-flex">
					                <div class="media-title bmedium"><?php echo $teacher[0]->name . " " . $teacher[0]->surname . "<br> (" . $teacher[0]->employee_id . ") " . $class_teacher ?>	 
					                </div>
					                 
			            		</div>  
			                </li>
							
			                <?php } ?>		            		                
			            </ul> 
						<?php  }else{ ?>
							<img src="https://smart-school.in/ssappresource/images/addnewitem.svg"  width="150" class="center-block mt20">
						<?php } ?>
                    </div>
                </div>
			</div><!--./col-lg-4-->	
		</div><!--./row-->	
	</section>
</div>

<style>
    .table-bordered {
        border: 1px solid #ddd;
    }
    .table-bordered > thead > tr > th,
    .table-bordered > tbody > tr > td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: center;
    }
    .table-bordered > thead > tr > th {
        background-color: #f5f5f5;
        font-weight: bold;
    }
    .table-hover > tbody > tr:hover {
        background-color: #f9f9f9;
    }
    .attendance-overview {
        border-collapse: separate;
        border-spacing: 0;
        border: 1px solid #ddd;
        border-radius: 5px;
        overflow: hidden;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        margin-top: 5px;
    }
    .attendance-overview thead {
        background-color: #f8f9fa;
    }
    .attendance-overview th,
    .attendance-overview td {
        padding: 12px;
        text-align: center;
        border: none;
        border-bottom: 1px solid #ddd;
    }
    .attendance-overview th {
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.9em;
        color: #495057;
    }
    .attendance-overview td {
        font-size: 1.1em;
        color: #212529;
    }
    .attendance-overview tbody tr:last-child td {
        border-bottom: none;
    }
    .attendance-overview tbody tr:hover {
        background-color: #f1f3f5;
    }
    .mb10 {
        margin-bottom: 10px;
    }
    h5 {
        margin-bottom: 5px;
        color: #495057;
        font-weight: 600;
    }
    .attendance-subtitle {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
        font-size: 0.9em;
    }
    .mb10 {
        margin-bottom: 15px;
    }
    .attendance-overview .table-total {
        background-color: #f8f9fa;
        font-weight: 600;
    }
    .attendance-overview .table-total td {
        border-top: 2px solid #dee2e6;
    }
</style>

