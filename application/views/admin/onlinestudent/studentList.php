<?php $currency_symbol = $this->customlib->getSchoolCurrencyFormat();?>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Main content -->
    <section class="content">
        <div class="row">
            <!-- left column -->
            <div class="col-md-12">
                <!-- general form elements -->
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><?php echo $this->lang->line('student_list'); ?></h3>
                        <div class="box-tools pull-right">
                        </div><!-- /.box-tools -->
                    </div><!-- /.box-header -->
                    <div class="box-body">
                      <div class="table-responsive">
                        <div class="mailbox-messages">
                             <?php if ($this->session->flashdata('msg')) {
    echo $this->session->flashdata('msg');
    $this->session->unset_userdata('msg');}?>
                            <table class="table table-striped table-bordered table-hover student-list" data-export-title="<?php echo $this->lang->line('student_list'); ?>">
                                <thead>
                                    <tr>
                                        <th style="width:5%"><?php echo $this->lang->line('reference_no'); ?></th>
                                        <th><?php echo $this->lang->line('student_name'); ?></th>
                                        <th class="white-space-nowrap"><?php echo $this->lang->line('class'); ?></th>
                                         <?php if ($sch_setting->father_name) {?>
                                            <th><?php echo $this->lang->line('father_name'); ?></th>
                                        <?php }?>
                                        <th><?php echo $this->lang->line('date_of_birth'); ?></th>
                                        <th><?php echo $this->lang->line('gender'); ?></th>
                                        <th><?php echo $this->lang->line('category'); ?></th>
                                          <?php if ($sch_setting->mobile_no) {?>
                                        <th style="width:10%"><?php echo $this->lang->line('student_mobile_number'); ?></th>
                                       <?php }?>
                                        <th><?php echo $this->lang->line('form_status'); ?></th>
                                        <?php if ($sch_setting->online_admission_payment == 'yes') {?>
                                            <th><?php echo $this->lang->line('payment_status'); ?></th>
                                            <?php }?>
                                        <th><?php echo $this->lang->line('enrolled'); ?></th>
                                        <th><?php echo $this->lang->line('admission_date'); ?></th>
                                        <th class="text-right noExport"><?php echo $this->lang->line('action'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table><!-- /.table -->
                        </div><!-- /.mail-box-messages -->
                       </div><!--./table-responsive-->
                    </div><!-- /.box-body -->
                </div>
            </div><!--/.col (left) -->
            <!-- right column -->
        </div>
    </section><!-- /.content -->
</div><!-- /.content-wrapper -->

<script>
    ( function ( $ ) {
    'use strict';
    $(document).ready(function () {
        initDatatable('student-list','admin/onlinestudent/getstudentlist',[],[],100);
    });
} ( jQuery ) )

// Payment Modal Functions
function openPaymentModal(studentId) {
    // Get the student's name from the table row
    var row = $("a[onclick='openPaymentModal(" + studentId + ")']").closest('tr');
    var studentName = row.find('td').eq(1).text(); // 2nd column is student name
    $('#payment_student_name').text(studentName);
    $('#paymentModal').modal('show');
    $('#student_id').val(studentId);
}

function submitPayment() {
    var formData = $('#paymentForm').serialize();
    
    $.ajax({
        url: '<?php echo base_url(); ?>admin/onlinestudent/addPayment',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if(response.status == 1) {
                $('#paymentModal').modal('hide');
                alert(response.message);
                $('.student-list').DataTable().ajax.reload();
            } else {
                alert(response.message);
            }
        },
        error: function(xhr, status, error) {
            alert('AJAX error: ' + xhr.responseText);
        }
    });
}
</script>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1" role="dialog" aria-labelledby="paymentModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="paymentModalLabel"><?php echo $this->lang->line('add_payment'); ?></h4>
            </div>
            <div class="modal-body">
                <div style="font-weight:bold;margin-bottom:10px;">
                    <?php echo $this->lang->line('student_name'); ?>: <span id="payment_student_name"></span>
                </div>
                <form id="paymentForm">
                    <input type="hidden" id="student_id" name="student_id">
                    <div class="form-group">
                        <label for="payment_mode"><?php echo $this->lang->line('payment_mode'); ?></label>
                        <select class="form-control" id="payment_mode" name="payment_mode" required>
                            <option value=""><?php echo $this->lang->line('select'); ?></option>
                            <option value="cash"><?php echo $this->lang->line('cash'); ?></option>
                            <option value="cheque"><?php echo $this->lang->line('cheque'); ?></option>
                            <option value="upi"><?php echo $this->lang->line('upi_qr'); ?></option>
                            <option value="bank_transfer"><?php echo $this->lang->line('bank_transfer'); ?></option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="transaction_id"><?php echo $this->lang->line('transaction_id'); ?></label>
                        <input type="text" class="form-control" id="transaction_id" name="transaction_id" required>
                    </div>
                    <div class="form-group">
                        <label for="transaction_date"><?php echo $this->lang->line('transaction_date'); ?></label>
                        <input type="text" class="form-control date" id="transaction_date" name="transaction_date" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo $this->lang->line('close'); ?></button>
                <button type="button" class="btn btn-primary" onclick="submitPayment()"><?php echo $this->lang->line('submit'); ?></button>
            </div>
        </div>
    </div>
</div>

<script>
    function checkpaymentstatus(id){
       $.ajax({
            url: '<?php echo base_url(); ?>admin/onlinestudent/checkpaymentstatus',
            type: "POST",
            data: {id:id},
            success: function (data) {

               if(data!=""){
                    if(confirm(data)){
                      window.location.href="<?php echo base_url() . 'admin/onlinestudent/edit/' ?>"+id ;
                    }else{
                         return false ;
                    }
                }else{
                     window.location.href="<?php echo base_url() . 'admin/onlinestudent/edit/' ?>"+id ;
                }
            }
        });
    }
</script>